import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import migration from '../lib/db/migration';
import * as IndexedDB from '../lib/indexeddb';
import sqlitedb from '../lib/sqlitedb';
import { CheckCircle, AlertTriangle, Database, RefreshCw, ArrowRightLeft } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import { ClearIndexedDB } from "@/components/ClearIndexedDB";

enum MigrationStatus {
  IDLE = 'idle',
  CHECKING = 'checking',
  READY = 'ready',
  MIGRATING = 'migrating',
  COMPLETE = 'complete',
  ERROR = 'error'
}

interface MigrationStats {
  indexeddb?: {
    documents: number;
    versions: number;
    comments: number;
    edits: number;
  };
  sqlite?: {
    documents: number;
    versions: number;
    comments: number;
    edits: number;
  };
  matches?: {
    documents: boolean;
    versions: boolean;
    comments: boolean;
    edits: boolean;
  };
}

const MigrationUI: React.FC = () => {
  const [status, setStatus] = useState<MigrationStatus>(MigrationStatus.IDLE);
  const [progress, setProgress] = useState(0);
  const [stats, setStats] = useState<MigrationStats>({});
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  
  // Check if both databases are available and get initial record counts
  const checkDatabases = async () => {
    try {
      setStatus(MigrationStatus.CHECKING);
      setMessage('Checking database availability...');
      
      // Check IndexedDB
      if (!IndexedDB.isIndexedDBAvailable()) {
        throw new Error('IndexedDB is not available in this browser');
      }
      
      // Check SQLite
      await sqlitedb.initSQLite();
      if (!sqlitedb.isSQLiteAvailable()) {
        throw new Error('SQLite is not available');
      }
      
      // Get validation stats
      const validation = await migration.validateMigration();
      setStats(validation.details);
      
      setStatus(MigrationStatus.READY);
      setMessage('Both databases are available. Ready to migrate.');
    } catch (err: any) {
      setStatus(MigrationStatus.ERROR);
      setError(err.message);
    }
  };
  
  // Start the migration process
  const startMigration = async () => {
    try {
      setStatus(MigrationStatus.MIGRATING);
      setProgress(10);
      setMessage('Starting migration...');
      
      // Run the migration
      const result = await migration.migrateToSQLite();
      setProgress(80);
      
      // Validate the migration
      setMessage('Validating migration...');
      const validation = await migration.validateMigration();
      setStats(validation.details);
      setProgress(100);
      
      if (result.success && validation.success) {
        setStatus(MigrationStatus.COMPLETE);
        setMessage('Migration completed successfully!');
      } else {
        setStatus(MigrationStatus.ERROR);
        setError(result.message);
      }
    } catch (err: any) {
      setStatus(MigrationStatus.ERROR);
      setError(err.message);
    }
  };
  
  // Reset the migration UI
  const resetMigration = () => {
    setStatus(MigrationStatus.IDLE);
    setProgress(0);
    setMessage('');
    setError('');
    setStats({});
  };
  
  const renderStatusAlert = () => {
    switch (status) {
      case MigrationStatus.CHECKING:
        return (
          <Alert variant="default">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertTitle>Checking</AlertTitle>
            <AlertDescription>Checking database availability...</AlertDescription>
          </Alert>
        );
      case MigrationStatus.READY:
        return (
          <Alert variant="default">
            <Database className="h-4 w-4" />
            <AlertTitle>Ready</AlertTitle>
            <AlertDescription>Ready to migrate data from IndexedDB to SQLite.</AlertDescription>
          </Alert>
        );
      case MigrationStatus.MIGRATING:
        return (
          <Alert variant="default">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <AlertTitle>In Progress</AlertTitle>
            <AlertDescription>Migration in progress. Please wait...</AlertDescription>
          </Alert>
        );
      case MigrationStatus.COMPLETE:
        return (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>Migration completed successfully!</AlertDescription>
          </Alert>
        );
      case MigrationStatus.ERROR:
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        );
      default:
        return null;
    }
  };
  
  const renderStatsTable = () => {
    if (!stats.indexeddb || !stats.sqlite) {
      return null;
    }
    
    return (
      <div className="overflow-x-auto my-4">
        <table className="w-full border-collapse">
          <thead>
            <tr className="border-b">
              <th className="px-4 py-2 text-left">Data Type</th>
              <th className="px-4 py-2 text-center">IndexedDB</th>
              <th className="px-4 py-2 text-center">SQLite</th>
              <th className="px-4 py-2 text-center">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="px-4 py-2">Documents</td>
              <td className="px-4 py-2 text-center">{stats.indexeddb.documents}</td>
              <td className="px-4 py-2 text-center">{stats.sqlite.documents}</td>
              <td className="px-4 py-2 text-center">
                {stats.matches?.documents 
                  ? <CheckCircle className="h-4 w-4 text-green-500 inline" /> 
                  : <AlertTriangle className="h-4 w-4 text-red-500 inline" />}
              </td>
            </tr>
            <tr className="border-b">
              <td className="px-4 py-2">Versions</td>
              <td className="px-4 py-2 text-center">{stats.indexeddb.versions}</td>
              <td className="px-4 py-2 text-center">{stats.sqlite.versions}</td>
              <td className="px-4 py-2 text-center">
                {stats.matches?.versions 
                  ? <CheckCircle className="h-4 w-4 text-green-500 inline" /> 
                  : <AlertTriangle className="h-4 w-4 text-red-500 inline" />}
              </td>
            </tr>
            <tr className="border-b">
              <td className="px-4 py-2">Comments</td>
              <td className="px-4 py-2 text-center">{stats.indexeddb.comments}</td>
              <td className="px-4 py-2 text-center">{stats.sqlite.comments}</td>
              <td className="px-4 py-2 text-center">
                {stats.matches?.comments 
                  ? <CheckCircle className="h-4 w-4 text-green-500 inline" /> 
                  : <AlertTriangle className="h-4 w-4 text-red-500 inline" />}
              </td>
            </tr>
            <tr>
              <td className="px-4 py-2">Edits</td>
              <td className="px-4 py-2 text-center">{stats.indexeddb.edits}</td>
              <td className="px-4 py-2 text-center">{stats.sqlite.edits}</td>
              <td className="px-4 py-2 text-center">
                {stats.matches?.edits 
                  ? <CheckCircle className="h-4 w-4 text-green-500 inline" /> 
                  : <AlertTriangle className="h-4 w-4 text-red-500 inline" />}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  };
  
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" /> Database Migration
        </CardTitle>
        <CardDescription>
          Migrate data from IndexedDB to SQLite
        </CardDescription>
      </CardHeader>
      <CardContent>
        {renderStatusAlert()}
        
        {status === MigrationStatus.MIGRATING && (
          <div className="my-4">
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-center mt-2">{message}</p>
          </div>
        )}
        
        {renderStatsTable()}
        
        <div className="my-6 flex flex-col gap-4">
          {status === MigrationStatus.IDLE && (
            <Button onClick={checkDatabases} disabled={status !== MigrationStatus.IDLE}>
              <Database className="h-4 w-4 mr-2" />
              Check Databases
            </Button>
          )}
          
          {status === MigrationStatus.READY && (
            <Button onClick={startMigration} disabled={status !== MigrationStatus.READY}>
              <ArrowRightLeft className="h-4 w-4 mr-2" />
              Start Migration
            </Button>
          )}
          
          {status === MigrationStatus.CHECKING && (
            <Button disabled>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Checking Databases...
            </Button>
          )}
          
          {status === MigrationStatus.MIGRATING && (
            <Button disabled>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Migrating Data...
            </Button>
          )}
          
          {status === MigrationStatus.COMPLETE && (
            <div className="space-y-4">
              <Alert className="bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800">
                <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                <AlertTitle className="text-green-700 dark:text-green-300">Migration Complete</AlertTitle>
                <AlertDescription className="text-green-700 dark:text-green-300">
                  Your data has been successfully migrated to SQLite. You can now clear IndexedDB to free up space.
                </AlertDescription>
              </Alert>
              
              <div className="mt-4">
                <ClearIndexedDB />
              </div>
              
              <Button variant="outline" onClick={resetMigration}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset Migration Tool
              </Button>
            </div>
          )}
          
          {status === MigrationStatus.ERROR && (
            <Button variant="outline" onClick={resetMigration}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset Migration Tool
            </Button>
          )}
        </div>
        
        <div className="mt-8 bg-blue-50 dark:bg-blue-950 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-sm text-blue-700 dark:text-blue-300">
            <strong>Note:</strong> SQLite is now the default database for all new documents. This migration tool helps transfer existing documents from IndexedDB to SQLite.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default MigrationUI; 