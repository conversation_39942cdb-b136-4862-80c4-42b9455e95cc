'use client';

import { useParams } from 'next/navigation';
import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Loader2,
  Mail,
  Copy,
  Edit,
  CheckCircle2,
  AlertTriangle,
  Clock,
  Send,
} from 'lucide-react';

interface NotificationTemplate {
  id: string;
  name: string;
  type: 'award' | 'negotiation' | 'approval';
  subject: string;
  content: string;
  variables: {
    id: string;
    name: string;
    description: string;
  }[];
  lastUpdated: string;
}

interface NotificationHistory {
  id: string;
  templateId: string;
  recipientName: string;
  recipientEmail: string;
  subject: string;
  content: string;
  status: 'sent' | 'failed' | 'scheduled';
  sentAt?: string;
  scheduledFor?: string;
}

// Mock data - replace with actual API calls
const mockTemplates: NotificationTemplate[] = [
  {
    id: 't1',
    name: 'Award Decision - Successful',
    type: 'award',
    subject: 'Bid Award Notification - {{bidReference}}',
    content: `Dear {{vendorName}},

We are pleased to inform you that your bid submission for {{bidTitle}} (Reference: {{bidReference}}) has been successful.

Your proposal demonstrated exceptional understanding of our requirements and offered compelling value. The total contract value is {{contractValue}}, with a planned start date of {{startDate}}.

Our contracts team will be in touch shortly to initiate the contract finalization process.

Best regards,
{{organizationName}}`,
    variables: [
      {
        id: 'bidReference',
        name: 'Bid Reference',
        description: 'The unique reference number for this bid',
      },
      {
        id: 'vendorName',
        name: 'Vendor Name',
        description: 'The name of the vendor company',
      },
      {
        id: 'bidTitle',
        name: 'Bid Title',
        description: 'The title of the bid',
      },
      {
        id: 'contractValue',
        name: 'Contract Value',
        description: 'The total value of the contract',
      },
      {
        id: 'startDate',
        name: 'Start Date',
        description: 'The planned start date for the contract',
      },
      {
        id: 'organizationName',
        name: 'Organization Name',
        description: 'Your organization name',
      },
    ],
    lastUpdated: '2025-03-01T10:00:00Z',
  },
  {
    id: 't2',
    name: 'Contract Negotiation Meeting',
    type: 'negotiation',
    subject: 'Contract Negotiation Meeting - {{bidReference}}',
    content: `Dear {{vendorName}},

Following your successful bid for {{bidTitle}}, we would like to schedule a contract negotiation meeting.

Proposed Date: {{meetingDate}}
Time: {{meetingTime}}
Location: {{meetingLocation}}

Please confirm your availability or suggest alternative times if this doesn't suit your schedule.

Agenda:
1. Review of contract terms
2. Discussion of deliverables and timelines
3. Agreement on payment milestones
4. Next steps

Best regards,
{{organizationName}}`,
    variables: [
      {
        id: 'vendorName',
        name: 'Vendor Name',
        description: 'The name of the vendor company',
      },
      {
        id: 'bidReference',
        name: 'Bid Reference',
        description: 'The unique reference number for this bid',
      },
      {
        id: 'bidTitle',
        name: 'Bid Title',
        description: 'The title of the bid',
      },
      {
        id: 'meetingDate',
        name: 'Meeting Date',
        description: 'The proposed date for the meeting',
      },
      {
        id: 'meetingTime',
        name: 'Meeting Time',
        description: 'The proposed time for the meeting',
      },
      {
        id: 'meetingLocation',
        name: 'Meeting Location',
        description: 'The location or virtual meeting link',
      },
      {
        id: 'organizationName',
        name: 'Organization Name',
        description: 'Your organization name',
      },
    ],
    lastUpdated: '2025-03-02T10:00:00Z',
  },
];

const mockHistory: NotificationHistory[] = [
  {
    id: 'n1',
    templateId: 't1',
    recipientName: 'Tech Solutions Inc.',
    recipientEmail: '<EMAIL>',
    subject: 'Bid Award Notification - BID2025-001',
    content: 'Award notification content...',
    status: 'sent',
    sentAt: '2025-03-15T10:00:00Z',
  },
  {
    id: 'n2',
    templateId: 't2',
    recipientName: 'Tech Solutions Inc.',
    recipientEmail: '<EMAIL>',
    subject: 'Contract Negotiation Meeting - BID2025-001',
    content: 'Meeting invitation content...',
    status: 'scheduled',
    scheduledFor: '2025-03-20T10:00:00Z',
  },
];

const notificationSchema = z.object({
  templateId: z.string().min(1, 'Please select a template'),
  recipientName: z.string().min(1, 'Please enter recipient name'),
  recipientEmail: z.string().email('Please enter a valid email'),
  subject: z.string().min(1, 'Please enter a subject'),
  content: z.string().min(1, 'Please enter content'),
  scheduledFor: z.string().optional(),
});

type NotificationForm = z.infer<typeof notificationSchema>;

export default function NotificationsPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<NotificationTemplate | null>(null);
  const [templates] = useState<NotificationTemplate[]>(mockTemplates);
  const [history] = useState<NotificationHistory[]>(mockHistory);

  const form = useForm<NotificationForm>({
    resolver: zodResolver(notificationSchema),
    defaultValues: {
      templateId: '',
      recipientName: '',
      recipientEmail: '',
      subject: '',
      content: '',
    },
  });

  const onSubmit = async (data: NotificationForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to send notification
      console.log('Sending notification:', data);

      toast({
        title: "Notification Sent",
        description: "The notification has been sent successfully.",
      });

      form.reset();
    } catch (error) {
      console.error('Error sending notification:', error);
      toast({
        title: "Sending Failed",
        description: "There was an error sending the notification.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
    if (template) {
      form.setValue('templateId', templateId);
      form.setValue('subject', template.subject);
      form.setValue('content', template.content);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Sent
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Scheduled
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Notification Templates</CardTitle>
            <CardDescription>
              Manage and send notifications for bid {bidId}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="compose">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="compose">Compose</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>

              <TabsContent value="compose">
                <Card>
                  <CardHeader>
                    <CardTitle>Send Notification</CardTitle>
                    <CardDescription>
                      Select a template and customize the notification
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="templateId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Template</FormLabel>
                              <Select
                                onValueChange={(value) => handleTemplateChange(value)}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a template" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {templates.map((template) => (
                                    <SelectItem
                                      key={template.id}
                                      value={template.id}
                                    >
                                      <div>
                                        {template.name}
                                        <div className="text-xs text-gray-500">
                                          Last updated: {formatDate(template.lastUpdated)}
                                        </div>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {selectedTemplate && (
                          <>
                            <div className="grid grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="recipientName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Recipient Name</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Enter recipient name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="recipientEmail"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Recipient Email</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Enter recipient email" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <FormField
                              control={form.control}
                              name="subject"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Subject</FormLabel>
                                  <FormControl>
                                    <Input {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="content"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Content</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      className="min-h-[300px] font-mono"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="scheduledFor"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Schedule For (Optional)</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="datetime-local"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="bg-accent/50 rounded-lg p-4">
                              <h4 className="text-sm font-medium mb-2">Available Variables</h4>
                              <div className="grid grid-cols-2 gap-2">
                                {selectedTemplate.variables.map((variable) => (
                                  <div
                                    key={variable.id}
                                    className="flex items-start space-x-2"
                                  >
                                    <Button
                                      variant="outline"
                                      size="icon"
                                      className="mt-0.5"
                                      onClick={() => {
                                        const content = form.getValues('content');
                                        const variableText = `{{${variable.id}}}`;
                                        form.setValue('content', content + variableText);
                                      }}
                                    >
                                      <Copy className="h-3 w-3" />
                                    </Button>
                                    <div>
                                      <div className="text-sm font-medium">
                                        {variable.name}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {variable.description}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </>
                        )}

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isSubmitting || !selectedTemplate}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Sending...
                            </>
                          ) : (
                            <>
                              <Send className="mr-2 h-4 w-4" />
                              Send Notification
                            </>
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification History</CardTitle>
                    <CardDescription>
                      Track sent and scheduled notifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Recipient</TableHead>
                          <TableHead>Subject</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Sent/Scheduled</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {history.map((notification) => (
                          <TableRow key={notification.id}>
                            <TableCell>
                              <div>
                                {notification.recipientName}
                                <div className="text-sm text-gray-500">
                                  {notification.recipientEmail}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{notification.subject}</TableCell>
                            <TableCell>{getStatusBadge(notification.status)}</TableCell>
                            <TableCell>
                              {notification.sentAt
                                ? formatDate(notification.sentAt)
                                : notification.scheduledFor
                                ? formatDate(notification.scheduledFor)
                                : '-'}
                            </TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => {
                                    // TODO: Implement view action
                                    console.log('View notification:', notification.id);
                                  }}
                                >
                                  <Mail className="h-4 w-4" />
                                </Button>
                                {notification.status === 'scheduled' && (
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => {
                                      // TODO: Implement edit action
                                      console.log('Edit notification:', notification.id);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
