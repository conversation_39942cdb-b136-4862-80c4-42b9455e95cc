'use client';

import { usePara<PERSON> } from 'next/navigation';
import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Loader2, CheckCircle2, Clock, AlertTriangle, FileCheck, XCircle } from 'lucide-react';

interface Approver {
  id: string;
  name: string;
  role: string;
  order: number;
  status: 'pending' | 'approved' | 'rejected';
  comment?: string;
  timestamp?: string;
}

interface AwardRecommendation {
  id: string;
  bidId: string;
  selectedVendorId: string;
  vendorName: string;
  justification: string;
  riskMitigation: string;
  nextSteps: string;
  contractTerms: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    role: string;
  };
}

// Mock data - replace with actual API calls
const mockRecommendation: AwardRecommendation = {
  id: 'rec1',
  bidId: 'bid123',
  selectedVendorId: 'v1',
  vendorName: 'Tech Solutions Inc.',
  justification: 'Tech Solutions Inc. demonstrated superior technical capabilities...',
  riskMitigation: 'Key risks will be mitigated through...',
  nextSteps: '1. Contract negotiation\n2. Project kickoff planning...',
  contractTerms: 'Payment terms: Net 30\nWarranty: 12 months...',
  status: 'pending',
  createdAt: '2025-03-15T10:00:00Z',
  createdBy: {
    id: 'u1',
    name: 'John Smith',
    role: 'Bid Manager',
  },
};

const mockApprovers: Approver[] = [
  {
    id: 'a1',
    name: 'Sarah Johnson',
    role: 'Technical Director',
    order: 1,
    status: 'approved',
    comment: 'Technical solution meets all requirements.',
    timestamp: '2025-03-15T11:00:00Z',
  },
  {
    id: 'a2',
    name: 'Michael Chen',
    role: 'Commercial Manager',
    order: 2,
    status: 'pending',
  },
  {
    id: 'a3',
    name: 'Lisa Brown',
    role: 'Legal Counsel',
    order: 3,
    status: 'pending',
  },
  {
    id: 'a4',
    name: 'David Wilson',
    role: 'CFO',
    order: 4,
    status: 'pending',
  },
];

const approvalSchema = z.object({
  decision: z.enum(['approved', 'rejected']),
  comment: z.string().min(10, 'Please provide a comment explaining your decision'),
});

type ApprovalForm = z.infer<typeof approvalSchema>;

export default function ApprovalPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [recommendation] = useState<AwardRecommendation>(mockRecommendation);
  const [approvers] = useState<Approver[]>(mockApprovers);

  // Mock current user - replace with actual auth
  const currentUser = {
    id: 'a2',
    name: 'Michael Chen',
    role: 'Commercial Manager',
  };

  const form = useForm<ApprovalForm>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      comment: '',
    },
  });

  const isCurrentApprover = () => {
    const currentApprover = approvers.find(a => a.status === 'pending');
    return currentApprover?.id === currentUser.id;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Approved
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
    }
  };

  const onSubmit = async (data: ApprovalForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit approval decision
      console.log('Submitting approval decision:', data);

      toast({
        title: `Recommendation ${data.decision === 'approved' ? 'Approved' : 'Rejected'}`,
        description: "Your decision has been recorded.",
      });

      form.reset();
    } catch (error) {
      console.error('Error submitting decision:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your decision.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Award Recommendation Approval</CardTitle>
                <CardDescription>
                  Review and approve the award recommendation for bid {bidId}
                </CardDescription>
              </div>
              {getStatusBadge(recommendation.status)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">Recommendation Details</h3>
                <div className="mt-4 space-y-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Recommended Vendor</div>
                    <div className="mt-1">{recommendation.vendorName}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Justification</div>
                    <div className="mt-1 whitespace-pre-wrap">{recommendation.justification}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Risk Mitigation</div>
                    <div className="mt-1 whitespace-pre-wrap">{recommendation.riskMitigation}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Next Steps</div>
                    <div className="mt-1 whitespace-pre-wrap">{recommendation.nextSteps}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Contract Terms</div>
                    <div className="mt-1 whitespace-pre-wrap">{recommendation.contractTerms}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Submitted By</div>
                    <div className="mt-1">
                      {recommendation.createdBy.name} ({recommendation.createdBy.role})
                      <div className="text-sm text-gray-500">
                        {formatDate(recommendation.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Approval Chain</h3>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Approver</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Comment</TableHead>
                      <TableHead>Timestamp</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {approvers.map((approver) => (
                      <TableRow key={approver.id}>
                        <TableCell className="font-medium">{approver.name}</TableCell>
                        <TableCell>{approver.role}</TableCell>
                        <TableCell>{getStatusBadge(approver.status)}</TableCell>
                        <TableCell>{approver.comment || '-'}</TableCell>
                        <TableCell>
                          {approver.timestamp ? formatDate(approver.timestamp) : '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {isCurrentApprover() && (
                <Card>
                  <CardHeader>
                    <CardTitle>Your Decision</CardTitle>
                    <CardDescription>
                      Submit your approval decision for this recommendation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="comment"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Comment</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Provide a comment explaining your decision..."
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex space-x-4">
                          <Button
                            type="submit"
                            className="flex-1"
                            onClick={() => form.setValue('decision', 'approved')}
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Approving...
                              </>
                            ) : (
                              <>
                                <CheckCircle2 className="mr-2 h-4 w-4" />
                                Approve
                              </>
                            )}
                          </Button>
                          <Button
                            type="submit"
                            variant="destructive"
                            className="flex-1"
                            onClick={() => form.setValue('decision', 'rejected')}
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Rejecting...
                              </>
                            ) : (
                              <>
                                <XCircle className="mr-2 h-4 w-4" />
                                Reject
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
