import { DOM<PERSON>arser } from '@xmldom/xmldom';

/**
 * Represents a SAML artifact as a JSON object, containing the artifact value and
 * a timestamp of when it was stored.
 */
export interface SAMLArtifact {
  /**
   * The SAML artifact value.
   */
  value: string;
  /**
   * The timestamp of when the artifact was stored, in ISO string format.
   */
  timestamp: string;
}

/**
 * Extracts a SAML artifact from a given SAML response string.
 * @param samlResponse The SAML response string to extract the artifact from.
 * @returns The extracted SAML artifact value, or null if no artifact was found.
 */
export const extractSAMLArtifact = (samlResponse: string): string | null => {
  try {
    // Parse the SAML response string using an XML parser
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(samlResponse, 'text/xml');

    // Find the AssertionArtifact element
    const artifactElement = xmlDoc.getElementsByTagName('AssertionArtifact')[0];
    
    // Return the artifact value if found
    return artifactElement?.textContent || null;
  } catch (error) {
    // Log an error if the SAML response could not be parsed
    console.error('Failed to parse SAML response:', error);
    return null;
  }
};

export const storeSAMLArtifact = (artifact: string) => {
  if (typeof window === 'undefined') return; // Only run on client side
  
  /**
   * Represents the SAML artifact data to be stored.
   * The artifact is paired with a timestamp indicating when it was stored.
   */
  const samlData: SAMLArtifact = {
    // The SAML artifact value
    value: artifact,
    // The current timestamp in ISO string format
    timestamp: new Date().toISOString()
  };
  
  localStorage.setItem('samlArtifact', JSON.stringify(samlData));
};

/**
 * Retrieves the SAML artifact stored in the client's local storage.
 * 
 * @returns {SAMLArtifact | null} The SAML artifact object if present and valid, otherwise null.
 */
export const getSAMLArtifact = (): SAMLArtifact | null => {
  // Ensure the function only runs on the client side
  if (typeof window === 'undefined') return null;
  
  // Retrieve the stored SAML artifact data from local storage
  const storedData = localStorage.getItem('samlArtifact');
  if (!storedData) return null;
  
  try {
    // Parse and return the stored data as a SAMLArtifact object
    return JSON.parse(storedData) as SAMLArtifact;
  } catch {
    // Return null in case of a parsing error
    return null;
  }
};

/**
 * Clears the SAML artifact stored in the client's local storage.
 * 
 * This function should only be executed on the client side.
 */
export const clearSAMLArtifact = () => {
  if (typeof window === 'undefined') return; // Ensure the function only runs on the client side
  
  // Remove the SAML artifact data from local storage
  localStorage.removeItem('samlArtifact');
};
