"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, AlertCircle, Loader2, Filter, ChevronDown, ChevronUp } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { Bid, BidFilterParams } from "@/types";
import { fetchBids } from "@/lib/api";
import { useAuth } from "@/hooks/useAuth";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { BidFilter } from "@/components/bids/bid-filter";

const statusColors = {
  DRAFT: "bg-gray-500",
  PENDING: "bg-yellow-500",
  APPROVED: "bg-green-500",
  REJECTED: "bg-red-500",
  CLOSED: "bg-blue-500",
};

export default function BidsPage() {
  const [bids, setBids] = useState<Bid[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<BidFilterParams>({});

  useEffect(() => {
    const getBids = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await fetchBids(filters);
        setBids(data);
      } catch (err) {
        console.error("Error fetching bids:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch bids");
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      getBids();
    } else {
      setIsLoading(false);
    }
  }, [user, filters]);

  const handleFilterChange = (newFilters: BidFilterParams) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-4xl font-bold">Bids</h1>
        <div className="flex gap-4">
          <Button
            variant="outline"
            onClick={toggleFilters}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            {showFilters ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          <Link href="/bids/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Create New Bid
            </Button>
          </Link>
        </div>
      </div>

      {showFilters && (
        <div className="mb-6">
          <BidFilter
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
            initialFilters={filters}
          />
        </div>
      )}

      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading bids...</span>
        </div>
      )}

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!isLoading && !error && bids.length === 0 && (
        <div className="bg-card rounded-lg shadow p-12 text-center">
          <h2 className="text-xl font-semibold mb-2">No bids found</h2>
          <p className="text-muted-foreground mb-6">
            There are no bids available. Create your first bid to get started.
          </p>
          <Link href="/bids/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Create New Bid
            </Button>
          </Link>
        </div>
      )}

      {!isLoading && !error && bids.length > 0 && (
        <div className="bg-card rounded-lg shadow">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Opportunity Source</TableHead>
                <TableHead>Business Need</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bids.map((bid) => (
                <TableRow key={bid.bidId}>
                  <TableCell>
                    <Link
                      href={`/bids/${bid.bidId}`}
                      className="text-primary hover:underline"
                    >
                      {bid.title}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{bid.status}</Badge>
                  </TableCell>
                  <TableCell>${bid.budget.toLocaleString()}</TableCell>
                  <TableCell>{bid.opportunitySource || "N/A"}</TableCell>
                  <TableCell>{bid.businessNeed || "N/A"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}