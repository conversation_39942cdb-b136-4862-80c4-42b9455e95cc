export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { headers } from 'next/headers';

// Define allowed origins (keeping consistent with other proxies)
const allowedOrigins = [
  process.env.NEXT_PUBLIC_APP_URL,
  'http://localhost:3000',
  'http://dbsapp.dcxeim.local:81'
];

export async function OPTIONS() {
  const headersList = headers();
  const origin = headersList.get('origin') || '';

  // Check if the origin is allowed
  if (allowedOrigins.includes(origin)) {
    return new NextResponse(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, SOAPAction',
        'Access-Control-Max-Age': '86400',
      },
    });
  }
  return new NextResponse(null, { status: 204 });
}

/**
 * Proxies PUT requests to the AppWorks entity API endpoint for updating Bid entities,
 * passing through the request body and headers with the SAML token.
 * 
 * @param request The Next.js request object
 * @param params Route parameters including bidId
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { bidId: string } }
) {
  try {
    const headersList = headers();
    const origin = headersList.get('origin') || '';
    
    // Get the SAMLart token from the request headers
    const samlToken = headersList.get('Authorization')?.replace('Bearer ', '');
    
    if (!samlToken) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'Missing authentication token',
        },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Get the bid ID from the route params
    const { bidId } = params;
    
    // Construct the AppWorks entity endpoint URL for updating bids
    // Format: {NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/{bidId}
    const appWorksUrl = `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/${bidId}`;
    
    console.log('Making PUT request to AppWorks Bid API:', {
      url: appWorksUrl,
      bidId,
      bodyPreview: JSON.stringify(body).substring(0, 200) + '...',
    });
    
    // Make the request to AppWorks
    const response = await axios.put(appWorksUrl, body, {
      headers: {
        'Content-Type': 'application/json',
        'SAMLart': samlToken
      },
      proxy: false,
    });
    
    console.log('AppWorks Bid update API response:', {
      status: response.status,
      statusText: response.statusText,
    });
    
    // Prepare response headers
    const responseHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (allowedOrigins.includes(origin)) {
      responseHeaders['Access-Control-Allow-Origin'] = origin;
    }
    
    // For successful updates, Appworks returns 204 No Content
    if (response.status === 204) {
      return new NextResponse(null, {
        status: 204,
        headers: responseHeaders,
      });
    }
    
    return NextResponse.json(response.data || {}, {
      status: response.status,
      headers: responseHeaders,
    });
  } catch (error: any) {
    console.error('AppWorks Bid update proxy error:', {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Failed to update bid in AppWorks',
        details: error.message,
        entityResponse: error.response?.data
      },
      { status: error.response?.status || 500 }
    );
  }
}
