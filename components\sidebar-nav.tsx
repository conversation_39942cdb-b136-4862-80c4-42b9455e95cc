"use client";

import Link from "next/link";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";
import { 
  BarChart2, 
  FileText, 
  Settings, 
  MessageSquare, 
  FilePlus, 
  Database,
  User,
  LogOut,
  LogIn,
  ArrowRightLeft
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useState } from "react";

interface NavItem {
  title: string;
  href: string;
  icon: React.ElementType;
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/",
    icon: BarChart2,
  },
  {
    title: "Bids",
    href: "/bids",
    icon: FileText,
  },
  {
    title: "New Bid",
    href: "/bids/new",
    icon: FilePlus,
  },
  {
    title: "Messages",
    href: "/messages",
    icon: MessageSquare,
  },
  {
    title: "Database",
    href: "/admin/dashboard",
    icon: Database,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
];

export function SidebarNav() {
  const pathname = usePathname();
  const { user, logout, isLoading } = useAuth();
  const { toast } = useToast();
  const [databaseOpen, setDatabaseOpen] = useState(false);

  const handleLogout = () => {
    logout();
    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });
  };

  return (
    <nav className="w-64 bg-background border-r h-screen py-6 px-3 hidden md:block">
      <div className="mb-8 px-4">
        <h1 className="font-bold text-xl">Bid Management</h1>
      </div>
      <div className="space-y-1">
        {navItems.filter(item => item.title !== "Database").map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center py-2 px-4 text-sm rounded-md transition-colors",
              pathname === item.href 
                ? "bg-accent text-accent-foreground font-medium" 
                : "hover:bg-accent/50"
            )}
          >
            <item.icon className="mr-3 h-4 w-4" />
            {item.title}
          </Link>
        ))}

        {/* Database Collapsible */}
        <Collapsible
          open={databaseOpen}
          onOpenChange={setDatabaseOpen}
          className="w-full"
        >
          <CollapsibleTrigger className={cn(
            "flex items-center py-2 px-4 text-sm rounded-md transition-colors w-full",
            pathname.startsWith("/admin") 
              ? "bg-accent text-accent-foreground font-medium" 
              : "hover:bg-accent/50"
          )}>
            <Database className="mr-3 h-4 w-4" />
            <span className="flex-1 text-left">Database</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={cn(
                "h-4 w-4 transition-transform duration-200",
                databaseOpen ? "rotate-180 transform" : ""
              )}
            >
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </CollapsibleTrigger>
          <CollapsibleContent className="pl-8">
            <Link
              href="/admin/dashboard"
              className={cn(
                "flex items-center py-2 px-4 text-sm rounded-md transition-colors",
                pathname === "/admin/dashboard"
                  ? "bg-accent text-accent-foreground font-medium" 
                  : "hover:bg-accent/50"
              )}
            >
              <Database className="mr-3 h-4 w-4" />
              Dashboard
            </Link>
            <Link
              href="/admin/migrate"
              className={cn(
                "flex items-center py-2 px-4 text-sm rounded-md transition-colors",
                pathname === "/admin/migrate"
                  ? "bg-accent text-accent-foreground font-medium" 
                  : "hover:bg-accent/50"
              )}
            >
              <ArrowRightLeft className="mr-3 h-4 w-4" />
              Migration
            </Link>
          </CollapsibleContent>
        </Collapsible>
      </div>
      
      {/* User login/logout section */}
      <div className="absolute bottom-8 left-0 w-64 px-5">
        <div className="border-t pt-4 mt-4">
          {isLoading ? (
            <div className="flex items-center py-2 px-4 text-sm">
              <User className="mr-3 h-4 w-4 animate-pulse" />
              Loading...
            </div>
          ) : user ? (
            <div className="space-y-2">
              <div className="flex items-center py-2 px-2 text-sm font-medium">
                <User className="mr-3 h-4 w-4" />
                {user.userId || 'User'}
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
                onClick={handleLogout}
              >
                <LogOut className="mr-3 h-4 w-4" />
                Logout
              </Button>
            </div>
          ) : (
            <Link
              href="/login"
              className={cn(
                "flex items-center py-2 px-4 text-sm rounded-md transition-colors hover:bg-accent/50"
              )}
            >
              <LogIn className="mr-3 h-4 w-4" />
              Login
            </Link>
          )}
        </div>
      </div>
    </nav>
  );
} 