"use client";

import { DocumentComment } from "@/types";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  MessageSquare, 
  Send, 
  Check, 
  X, 
  Reply,
  User
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface DocumentCommentsProps {
  documentId: string;
  versionId?: string;
}

export function DocumentComments({
  documentId,
  versionId
}: DocumentCommentsProps) {
  const [comments, setComments] = useState<DocumentComment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  useEffect(() => {
    // This would be replaced with an API call to fetch comments
    // For now, we'll use mock data
    const mockComments: DocumentComment[] = [
      {
        id: "1",
        documentId,
        content: "The introduction needs more context about the project goals.",
        author: {
          id: "user1",
          name: "John Doe",
          avatar: ""
        },
        createdAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        selection: {
          from: 0,
          to: 100
        },
        resolved: false,
        replies: [
          {
            id: "2",
            documentId,
            content: "I agree, I'll update it in the next version.",
            author: {
              id: "user2",
              name: "Jane Smith",
              avatar: ""
            },
            createdAt: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
            resolved: false
          }
        ]
      },
      {
        id: "3",
        documentId,
        content: "The technical approach section is too vague. Can we add more specific implementation details?",
        author: {
          id: "user3",
          name: "Bob Johnson",
          avatar: ""
        },
        createdAt: new Date(Date.now() - 21600000).toISOString(), // 6 hours ago
        selection: {
          from: 500,
          to: 600
        },
        resolved: false
      }
    ];

    setComments(mockComments);
  }, [documentId, versionId]);

  const handleAddComment = async () => {
    if (!user || !newComment.trim()) return;
    
    setIsLoading(true);
    
    try {
      // This would be replaced with an API call to add a comment
      const newCommentObj: DocumentComment = {
        id: crypto.randomUUID(),
        documentId,
        versionId,
        content: newComment,
        author: {
          id: user.userId || 'unknown',
          name: user.userId || 'Anonymous',
          avatar: ""
        },
        createdAt: new Date().toISOString(),
        resolved: false
      };
      
      setComments(prev => [newCommentObj, ...prev]);
      setNewComment("");
      
      toast({
        title: "Comment added",
        description: "Your comment has been added successfully.",
      });
    } catch (error) {
      console.error("Error adding comment:", error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddReply = async (commentId: string) => {
    if (!user || !replyContent.trim()) return;
    
    setIsLoading(true);
    
    try {
      // This would be replaced with an API call to add a reply
      const reply: DocumentComment = {
        id: crypto.randomUUID(),
        documentId,
        versionId,
        content: replyContent,
        author: {
          id: user.userId || 'unknown',
          name: user.userId || 'Anonymous',
          avatar: ""
        },
        createdAt: new Date().toISOString(),
        resolved: false
      };
      
      setComments(prev => 
        prev.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              replies: [...(comment.replies || []), reply]
            };
          }
          return comment;
        })
      );
      
      setReplyContent("");
      setReplyTo(null);
      
      toast({
        title: "Reply added",
        description: "Your reply has been added successfully.",
      });
    } catch (error) {
      console.error("Error adding reply:", error);
      toast({
        title: "Error",
        description: "Failed to add reply. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResolveComment = async (commentId: string) => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      // This would be replaced with an API call to resolve a comment
      setComments(prev => 
        prev.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              resolved: true
            };
          }
          return comment;
        })
      );
      
      toast({
        title: "Comment resolved",
        description: "The comment has been marked as resolved.",
      });
    } catch (error) {
      console.error("Error resolving comment:", error);
      toast({
        title: "Error",
        description: "Failed to resolve comment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffMins < 1) return "just now";
    if (diffMins < 60) return `${diffMins} min${diffMins !== 1 ? "s" : ""} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? "s" : ""} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? "s" : ""} ago`;
    
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    });
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-xl">Comments</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 p-0">
        <div className="p-4 border-b">
          <Textarea
            placeholder="Add a new comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            className="mb-2 min-h-[100px]"
          />
          <Button
            onClick={handleAddComment}
            disabled={isLoading || !newComment.trim()}
            className="w-full"
          >
            <Send className="h-4 w-4 mr-2" />
            Add Comment
          </Button>
        </div>
        
        {comments.length === 0 ? (
          <div className="p-8 text-center flex-1 flex flex-col items-center justify-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No comments yet</h3>
            <p className="text-muted-foreground max-w-md">
              Add a comment to start a discussion about this document.
            </p>
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-450px)]">
            <div className="p-4 space-y-6">
              {comments.map((comment) => (
                <div key={comment.id} className="space-y-4">
                  <div className="p-4 border rounded-lg bg-muted/30">
                    <div className="flex items-start gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={comment.author.avatar} />
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <div className="font-semibold">{comment.author.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {formatDate(comment.createdAt)}
                          </div>
                        </div>
                        <div className="mt-1">{comment.content}</div>
                        <div className="mt-3 flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={() => setReplyTo(comment.id === replyTo ? null : comment.id)}
                          >
                            <Reply className="h-3.5 w-3.5 mr-1" />
                            Reply
                          </Button>
                          {!comment.resolved && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 px-2 text-xs"
                              onClick={() => handleResolveComment(comment.id)}
                            >
                              <Check className="h-3.5 w-3.5 mr-1" />
                              Resolve
                            </Button>
                          )}
                          {comment.resolved && (
                            <Badge variant="outline" className="bg-green-100 text-green-800 text-xs py-0">
                              <Check className="h-3 w-3 mr-1" />
                              Resolved
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="ml-8 pl-4 border-l-2 border-muted space-y-3">
                      {comment.replies.map((reply) => (
                        <div key={reply.id} className="p-3 border rounded-lg bg-muted/20">
                          <div className="flex items-start gap-3">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={reply.author.avatar} />
                              <AvatarFallback>
                                <User className="h-3 w-3" />
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div className="font-medium text-sm">{reply.author.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {formatDate(reply.createdAt)}
                                </div>
                              </div>
                              <div className="mt-1 text-sm">{reply.content}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Reply input */}
                  {replyTo === comment.id && (
                    <div className="ml-8 pl-4 border-l-2 border-muted">
                      <div className="p-3 border rounded-lg bg-muted/20">
                        <Textarea
                          placeholder={`Reply to ${comment.author.name}...`}
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          className="text-sm min-h-[80px] mb-2"
                        />
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setReplyTo(null);
                              setReplyContent("");
                            }}
                          >
                            <X className="h-3.5 w-3.5 mr-1" />
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleAddReply(comment.id)}
                            disabled={isLoading || !replyContent.trim()}
                          >
                            <Send className="h-3.5 w-3.5 mr-1" />
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
