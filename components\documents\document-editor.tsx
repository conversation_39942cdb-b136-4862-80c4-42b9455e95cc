"use client";

import { DocumentEditorProps, DocumentVersion } from "@/types";
import { useEffect, useState, useCallback, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { 
  Save, 
  History, 
  MessageSquare, 
  FileText, 
  Eye,
  Download,
  Loader2,
  Wifi,
  WifiOff,
  Clock,
  Upload
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/hooks/useAuth";
import { useIndexedDB } from "@/hooks/useIndexedDB";
import { useAppworksDocument } from "@/hooks/useAppworksDocument";
import { DocumentToolbar } from "./document-toolbar";
import { DocumentVersionHistory } from "./document-version-history";
import { DocumentComments } from "./document-comments";
import { TipTapEditor } from "./tiptap-editor";
import { Editor } from "@tiptap/react";

export function DocumentEditor({ 
  documentId, 
  initialContent = "", 
  readOnly = false,
  bidId,
  onSave,
  onVersionCreate
}: DocumentEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [isEditing, setIsEditing] = useState(!readOnly);
  const [commitMessage, setCommitMessage] = useState("");
  const [activeTab, setActiveTab] = useState("editor");
  const [editor, setEditor] = useState<Editor | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Use our IndexedDB hook
  const { 
    isDBAvailable,
    isOnline,
    isLoading: isDbLoading,
    document: localDocument,
    versions,
    saveDocumentContent, 
    createVersion,
    restoreVersion,
  } = useIndexedDB(documentId);

  // Use our Appworks document hook
  const {
    isLoading: isAppworksLoading,
    uploadDocument,
  } = useAppworksDocument(bidId);

  // Set up a ref to capture the editor instance
  const editorInstanceRef = useCallback((editorInstance: Editor) => {
    if (editorInstance) {
      console.log('Editor instance received in document-editor component');
      setEditor(editorInstance);
    }
  }, []);

  // Add a debug effect to track editor updates
  useEffect(() => {
    if (editor) {
      console.log('Editor state updated in document-editor:', 
        'isEditable:', editor.isEditable,
        'has bulletList extension:', !!editor.can().toggleBulletList(),
        'has orderedList extension:', !!editor.can().toggleOrderedList(),
        'has heading extension:', !!editor.can().toggleHeading({ level: 1 })
      );
    }
  }, [editor]);

  // Load content from IndexedDB when component mounts
  useEffect(() => {
    const loadInitialContent = async () => {
      if (isDBAvailable && documentId && localDocument) {
        // Use local content if available, otherwise use initialContent
        setContent(localDocument.content || initialContent);
      } else {
        setContent(initialContent);
      }
    };
    
    loadInitialContent();
  }, [isDBAvailable, documentId, localDocument, initialContent]);

  // Debounced content change handler for auto-saving
  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    
    // Auto-save to IndexedDB after 2 seconds of inactivity
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    
    debounceTimerRef.current = setTimeout(async () => {
      if (isDBAvailable && documentId) {
        try {
          await saveDocumentContent(documentId, newContent);
        } catch (error) {
          console.error("Error auto-saving content:", error);
        }
      }
    }, 2000);
  }, [isDBAvailable, documentId, saveDocumentContent]);

  // Clean up debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  const handleSave = useCallback(async () => {
    if (!user) return;
    
    try {
      // Save to IndexedDB first
      if (isDBAvailable && documentId) {
        await saveDocumentContent(documentId, content);
      }
      
      // Call external onSave handler (likely API call)
      if (onSave) {
        onSave(content);
      }
      
      toast({
        title: "Document saved",
        description: isOnline 
          ? "Your changes have been saved successfully."
          : "Your changes have been saved locally and will sync when you're back online.",
      });
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Error",
        description: "Failed to save the document. Please try again.",
        variant: "destructive",
      });
    }
  }, [content, documentId, isDBAvailable, isOnline, onSave, saveDocumentContent, toast, user]);

  // Upload document to Appworks
  const handleUploadToAppworks = useCallback(async () => {
    if (!user || !content || !documentId || !bidId) {
      toast({
        title: "Upload Error",
        description: "Missing required information for upload.",
        variant: "destructive",
      });
      return;
    }
    
    setIsUploading(true);
    try {
      // Get the document title from IndexedDB or use a default
      const documentTitle = localDocument?.title || `Document-${documentId}`;
      const documentDescription = localDocument?.description || 'Document created from bid management system';
      
      // Upload the document to Appworks with our updated hook
      const result = await uploadDocument({
        fileName: `${documentTitle}.html`,
        description: documentDescription,
        content: content,
        mimeType: 'text/html',
        convertToPdf: true // Convert HTML to PDF before uploading
      });
      
      if (result) {
        toast({
          title: "Document uploaded to Appworks",
          description: "Your document has been successfully uploaded to the bid workspace.",
        });
        
        // Also save a version to mark this upload
        if (isDBAvailable) {
          await createVersion({
            documentId,
            content,
            commitMessage: "Uploaded to Appworks",
            userId: user.userId || 'unknown',
            userName: user.userId || 'Anonymous',
            userRole: 'User'
          });
        }
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Upload Error",
        description: "Failed to upload the document to Appworks. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [bidId, content, documentId, isDBAvailable, localDocument, createVersion, uploadDocument, user, toast]);

  const handleCreateVersion = useCallback(async () => {
    if (!user || !commitMessage.trim() || !documentId) return;
    
    try {
      // Create a new version in IndexedDB
      const newVersion = await createVersion({
        documentId,
        content,
        commitMessage,
        userId: user.userId || 'unknown',
        userName: user.userId || 'Anonymous',
        userRole: 'User'
      });
      
      // We're no longer calling onVersionCreate here because it causes duplicate versions
      // The local version creation above is sufficient
      
      setCommitMessage("");
      
      toast({
        title: "Version created",
        description: newVersion 
          ? `Created version ${newVersion.version}` 
          : "Created new version",
      });
    } catch (error) {
      console.error("Error creating version:", error);
      toast({
        title: "Error",
        description: "Failed to create version. Please try again.",
        variant: "destructive",
      });
    }
  }, [content, documentId, commitMessage, user, createVersion, toast]);

  const handleRestoreVersion = useCallback(async (version: DocumentVersion) => {
    if (!user || !documentId) return;
    
    try {
      if (isDBAvailable) {
        await restoreVersion(
          version, 
          user.userId || 'unknown',
          user.userId || 'Anonymous', 
          'User'
        );
      }
      
      setContent(version.content);
      setActiveTab("editor");
      
      toast({
        title: "Version restored",
        description: `Restored version ${version.version}`,
      });
    } catch (error) {
      console.error("Error restoring version:", error);
      toast({
        title: "Error",
        description: "Failed to restore version. Please try again.",
        variant: "destructive",
      });
    }
  }, [documentId, isDBAvailable, restoreVersion, toast, user]);

  return (
    <div className="flex flex-col h-full">
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle>Document Editor</CardTitle>
              {/* Network status indicator */}
              {isDBAvailable && (
                <div className="flex items-center text-xs text-muted-foreground">
                  {isOnline ? (
                    <div className="flex items-center text-green-500">
                      <Wifi className="h-3 w-3 mr-1" />
                      <span>Online</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-amber-500">
                      <WifiOff className="h-3 w-3 mr-1" />
                      <span>Offline</span>
                    </div>
                  )}
                </div>
              )}
              {/* Show auto-save indicator when typing and saving */}
              {debounceTimerRef.current && (
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Saving...</span>
                </div>
              )}
            </div>
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(false)}
                    disabled={isDbLoading}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSave}
                    disabled={isDbLoading}
                  >
                    {isDbLoading ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Save
                  </Button>
                </>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  disabled={readOnly}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
              {bidId && isOnline && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleUploadToAppworks}
                  disabled={isUploading || isAppworksLoading}
                >
                  {isUploading || isAppworksLoading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Upload to Document Repository
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="flex-1 flex flex-col"
        >
          <TabsList className="mx-4">
            <TabsTrigger value="editor">Editor</TabsTrigger>
            <TabsTrigger value="versions">
              <History className="h-4 w-4 mr-2" />
              Versions
            </TabsTrigger>
            <TabsTrigger value="comments">
              <MessageSquare className="h-4 w-4 mr-2" />
              Comments
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="editor" className="flex-1 p-4 pt-2">
            <div className="flex flex-col h-full">
              {isEditing && (
                <div className="mb-2">
                  <DocumentToolbar editor={editor} />
                </div>
              )}
              
              <div className="flex-1 min-h-0">
                <TipTapEditor
                  content={content}
                  onChange={handleContentChange}
                  readOnly={!isEditing || readOnly}
                  className="h-full"
                  autoFocus={isEditing && !readOnly}
                  placeholder="Start writing your document..."
                  onEditorReady={editorInstanceRef}
                />
              </div>
              
              {isEditing && (
                <div className="mt-4">
                  <Separator className="my-2" />
                  <div className="flex flex-col gap-2">
                    <div className="text-sm font-medium">
                      Create Version
                    </div>
                    <div className="flex flex-col gap-2">
                      <Textarea
                        placeholder="Describe the changes in this version..."
                        value={commitMessage}
                        onChange={(e) => setCommitMessage(e.target.value)}
                        rows={2}
                      />
                      <Button
                        onClick={handleCreateVersion}
                        disabled={isDbLoading || !commitMessage.trim()}
                      >
                        <History className="h-4 w-4 mr-2" />
                        Create Version
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="versions" className="flex-1">
            <DocumentVersionHistory
              documentId={documentId}
              versions={versions}
              onRestore={handleRestoreVersion}
            />
          </TabsContent>
          
          <TabsContent value="comments" className="flex-1">
            <DocumentComments
              documentId={documentId}
              versionId={versions.length > 0 ? versions[0].id : undefined}
            />
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
}
