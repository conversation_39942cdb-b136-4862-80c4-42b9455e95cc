# Lessons

- For website image paths, always use the correct relative path (e.g., 'images/filename.png') and ensure the images directory exists
- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- When using Jest, a test suite can fail even if all individual tests pass, typically due to issues in suite-level setup code or lifecycle hooks
- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- Use 'gpt-4o' as the model name for OpenAI's GPT-4 with vision capabilities 
- For file uploads, using react-dropzone provides better UX with drag-and-drop support
- Toast notifications should be used for user feedback on important actions
- Form validation should be done both client-side (Zod) and server-side
- Always include proper error handling and user feedback
- In Next.js dynamic routes, use consistent parameter names across the application (e.g., always use [bidId] instead of mixing [id] and [bidId])
- When using date formatting in Next.js, always use explicit formatting options with `toLocaleDateString()` to ensure consistency between server and client rendering (e.g., `toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' })`) to prevent hydration errors
- For SAML authentication in Next.js, the `@xmldom/xmldom` package is required for XML parsing, along with its TypeScript type declarations (`@types/xmldom`)
- When making HTTP requests in Next.js applications, the axios library needs to be installed as a dependency (`npm install axios`) along with its TypeScript type declarations
- Next.js middleware cannot be used with static exports (`output: 'export'`). If you need middleware for authentication, you must use server-side rendering.
- When using context-based hooks like `useAuth`, ensure the provider component (e.g., `AuthProvider`) wraps the application in the root layout.
- For authentication in Next.js, the middleware is an ideal place to handle route protection and redirects for unauthenticated users.
- When implementing OTDS and AppWorks integration, proper CORS handling is essential in API routes that proxy requests to these services.
- When sending dates to the Appworks API, use the 'yyyy-MM-dd' format without time information to avoid parsing errors.
- When fetching data from Appworks REST API, be sure to include the SAMLart token in the Authorization header of the request.
- When transforming data between Appworks API format and app data model, carefully map fields and handle missing or optional properties gracefully.
- Always implement proper loading states, error handling, and empty state displays when fetching data from external APIs.
- Direct client-side API calls to external services like Appworks may result in CORS errors. Use API routes in Next.js to create proxy endpoints that make the actual API calls from the server side.
- When implementing proxy API routes for external services, mirror the request structure but add proper authentication and error handling on the server side.
- The "Redirect is not allowed for a preflight request" CORS error occurs when trying to make direct API calls to services that are redirecting requests. Always use proxy API routes in Next.js to avoid these issues.
- The "Bid not found" error when navigating from the bids list to a specific bid occurs when using mock data for bid details but real API data for the list page
- In a Next.js dynamic route like [bidId], ensure you're fetching data from the same source (API or mock) that's used to generate the list links
- Always prefer real API data over mock data when the API functionality is already implemented
- When updating Next.js pages to use real API data, remember to remove any static path generation that relies on mock data
- In Next.js, server components cannot access browser-specific APIs like localStorage, which is often used to store authentication tokens
- When moving authentication-dependent data fetching from server components to client components, ensure the client component has access to the auth context
- For dynamic routes that need authentication, prefer client-side data fetching over server-side data fetching when the auth token is stored in browser storage
- In Next.js with App Router, a common pattern is to have a minimal server component that passes parameters to a client component which handles data fetching
- In document versioning systems, avoid duplicate version creation by not triggering both the internal version creation method and the parent component's callback. In particular, when a child component already handles the version creation (including the storage operation), it shouldn't also call a callback passed as a prop that performs the same creation again.
- When working with IndexedDB, always verify records were successfully stored before redirecting users
- Include a small delay before navigating to ensure database operations have completed
- Use both hook-based and direct database access methods as complementary approaches
- Add proper error boundaries and user-friendly error messages for all database operations
- Ensure database schema (object stores and indexes) is properly initialized during first access
- When using TipTap editor with Tailwind CSS, create a dedicated CSS file to explicitly style headings and lists since Tailwind's reset CSS removes default styling. Apply appropriate classes in the editorProps configuration, and ensure proper extension configuration for heading and list functionality.
- Maintain consistent user interface patterns across different entry points to the same feature (e.g., creating documents from bid details or documents page should use the same dialog component)
- When using IndexedDB to store related data (like documents and their versions), always filter by the exact primary key when retrieving related records to prevent incorrect associations
- When installing npm packages that require TypeScript types, check if they need @types/* packages installed as well. For those without type declarations, use // @ts-ignore comments with explanatory notes
- In authentication workflows that use SAML, ensure you're properly retrieving the SAML artifact from storage rather than expecting it directly in the auth context
- When using static methods in service classes, access them through the class itself (AppworksDocumentService.methodName), not through an instance

# Scratchpad

## Lessons
- **Base64 Encoding**: Ensure that Base64 content is properly formatted before sending to the Appworks API. This includes:
  - Removing any data URI prefixes.
  - Stripping whitespace.
  - Ensuring proper padding.

- **Error Handling**: Enhance error messages for SOAP responses to provide clearer feedback on issues, especially related to document uploads.

## Recent Changes
- Updated the document upload method to handle Base64 content correctly.
- Changed button text from "Upload to Appworks" to "Upload to Document Repository" for better clarity.

## Current Task: Implementing Document Upload Feature with Appworks Integration

I'm implementing a comprehensive document upload system that integrates with the Appworks APIs to store documents in the central document management system.

#### Problem Analysis:
- Documents created and edited in the app need to be uploaded to Appworks for long-term storage and sharing
- Need to handle the Appworks REST API for retrieving BusinessWorkspaceId
- Need to handle the Appworks SOAP API for document upload
- Need proper authentication with SAML tokens
- Need to convert HTML content to PDF for standardized storage

#### Progress:
[X] 1. Create Appworks service integration
   - Created AppworksDocumentService class with static methods for API interactions
   - Implemented getBidBusinessWorkspaceId method to fetch the workspace ID for a bid
   - Implemented uploadDocument method to upload documents via SOAP API
   - Added methods for PDF conversion and document export

[X] 2. Create API proxy routes
   - Implemented /api/bids/[bidId]/workspace route for fetching BusinessWorkspaceId
   - Implemented /api/documents/upload route for uploading documents
   - Added proper authentication and error handling

[X] 3. Create custom React hook for document operations
   - Implemented useAppworksDocument hook for document management
   - Added methods for fetching BusinessWorkspaceId
   - Added document upload functionality with HTML to PDF conversion
   - Implemented proper loading states and error handling

[X] 4. Update DocumentEditor component
   - Integrated Appworks document upload functionality
   - Added UI elements for document uploading
   - Implemented feedback with toast notifications

[X] 5. Fix dependency and typing issues
   - Installed missing jspdf and html2canvas packages
   - Added proper type handling with @ts-ignore comments where needed
   - Fixed authentication token retrieval using getSAMLArtifact
   - Updated API routes to use static methods correctly

#### Lessons:
- When installing npm packages that require TypeScript types, check if they need @types/* packages installed as well. For those without type declarations, use // @ts-ignore comments with explanatory notes
- In authentication workflows that use SAML, ensure you're properly retrieving the SAML artifact from storage rather than expecting it directly in the auth context
- When using static methods in service classes, access them through the class itself (AppworksDocumentService.methodName), not through an instance

## Current Task: Implementing Document Version Control

I'm implementing a comprehensive document version control system for the bid management platform.

#### Problem Analysis:
- Documents need version history tracking with author, timestamp, and change information
- Need to calculate and visualize differences between document versions
- Need to support reverting to previous versions and comparing versions

#### Progress:
[X] 1. Implement core version control functionality
   - Created DocumentVersionControl class in lib/documents.ts
   - Implemented semantic versioning for documents (major.minor.patch)
   - Added methods for creating versions, comparing versions, and calculating diffs
   - Integrated diff-match-patch library for accurate text comparison

[X] 2. Create API endpoints for document management
   - Implemented routes for content retrieval and updates
   - Added routes for version creation and listing
   - Created a dedicated endpoint for version comparison
   - Added proper validation and error handling

[X] 3. Enhance document editor client components
   - Updated DocumentEditorClient to support version control functionality
   - Added version history display with author and timestamp information
   - Implemented side-by-side and unified diff views
   - Added color-coded change highlighting (additions, deletions, modifications)
   - Implemented version restoration functionality

[X] 4. Fix TypeScript and integration issues
   - Added TypeScript definitions for diff-match-patch library
   - Updated OTDSAuthResponse to include displayName and role properties
   - Used any typing strategically to resolve complex typing issues
   - Fixed bugs in diff calculation and rendering

#### Lessons:
- The diff-match-patch library provides powerful text difference calculation capabilities but requires careful typing in TypeScript
- When implementing version control, it's important to store comprehensive metadata for accountability

## Current Task: Fix Duplicate Document Versions

## Problem
When clicking the 'Create Version' button after providing version information and then clicking 'Save', duplicate entries were being created in the Version History view with different version numbers.

## Solution
Fixed the issue by removing the redundant call to the parent component's callback in the document-editor.tsx file. The component was creating a version directly with IndexedDB and then also calling the parent's onVersionCreate callback, which created a second version.

```tsx
// We're no longer calling onVersionCreate here because it causes duplicate versions
// The local version creation above is sufficient
// if (newVersion && onVersionCreate) {
//   onVersionCreate(newVersion);
// }
```

## Document Editing System Functions

### Save Function
The Save function updates the document content in IndexedDB without creating a new version entry in the version history. It:
- Updates the document content and timestamp
- If offline, saves an edit record to be synced later
- Shows a success toast notification to the user
- Handles different messaging for online/offline states

### Create Version Function
The Create Version function creates a new version entry in the version history with:
- A version number (following semantic versioning)
- User's commit message
- Author information
- Timestamp
- Diff data showing what changed from the previous version

### Key Differences
- **Save**: For regular content updates while working on a document
- **Create Version**: For creating meaningful checkpoints in the document's history that can be reverted to later

## Implementation Details
- Used IndexedDB for document storage and version control
- Implemented diff tracking using diff-match-patch library
- Added semantic versioning (major.minor.patch) for document versions
- Included comprehensive metadata with each version (author, timestamp, commit message)