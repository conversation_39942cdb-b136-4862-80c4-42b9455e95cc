# Bid APIs
Please note that the SAMLart token should be included in the Authorization header of the request. The parameter name should always be SAMLart.

## Create a new Bid item 
URL: Method: POST {NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid

### REQUEST Headers:
Content-Type: application/json
SAMLart: {SAML assertion}

### REQUEST Body EXAMPLE (application/json):
```json
{
  "Properties": {
    "Bid_Title": "string",
    "Bid_Description": "string",
    "Bid_Budget": 0,
    "Bid_Status": "string",
    "Bid_OpportunitySource": "string",
    "Bid_BusinessNeed": "string",
    "Bid_StrategicAlignment": "string",
    "Bid_PotentialBenefits": "string",
    "Bid_PreliminaryScope": "string",
    "Bid_Stakeholders": "string",
    "Bid_RiskAssessment": "string",
    "Bid_BidStrategy": "string",
    "Bid_CompetitiveAnalysis": "string",
    "Bid_WinStrategy": "string",
    "Bid_TechnicalApproach": "string",
    "Bid_PricingStrategy": "string",
    "Bid_QualityStandards": "string",
    "Bid_ComplianceRequirements": "string"
  }
}
```

### RESPONSE Body EXAMPLE (application/json):
```json
{
  "Identity": {
    "Id": "1"
  },
  "_links": {
    "self": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/1"
    }
  }
}
```

### RESPONSE CODES:
- 201: Success
- 400: Error
- 500: Error

### Notes:
-----------------------------------------------------START OF Retrieve Bid items API-------------------------------------------

## Retrieve Bid items 
URL: Method: POST {NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/lists/DefaultList

### REQUEST Headers:
Content-Type: application/json
SAMLart: {SAML assertion}

### REQUEST Body EXAMPLE listing ALL available parameters (application/json):
```json
{
  "distinct": 0,
  "skip": 0,
  "top": 0,
  "parameters": {
    "Properties.Bid_PricingStrategy": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_Status": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_PreliminaryScope": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_QualityStandards": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_Stakeholders": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_WinStrategy": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_TechnicalApproach": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_BidStrategy": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_Budget": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_OpportunitySource": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_CompetitiveAnalysis": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_RiskAssessment": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_BusinessNeed": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_Title": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_PotentialBenefits": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_Description": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    },
    "Properties.Bid_ComplianceRequirements": {
      "name": "string",
      "comparison": {
        "value": "string",
        "operator": "string"
      }
    }
  },
  "orderBy": [
    {
      "name": "string",
      "sortType": "string"
    }
  ],
  "select": [
    {
      "name": "string"
    }
  ]
}
```
### REQUEST Body EXAMPLE using single parameter (application/json):
```json
{
  "distinct": 0,
  "skip": 0,
  "top": 0,
  "parameters": {
    "Properties.Bid_Status": {
      "name": "string",
      "comparison": {
        "value": "DRAFT",
        "operator": "eq"
      }
    }
  }
}
```

### RESPONSE Body EXAMPLE for single item that is retrieved (application/json):
```json
{
  "page": {
    "skip": 0,
    "top": 0,
    "count": 1
  },
  "_links": {
    "self": {
      "href": "/DatacentrixBidManagement/entities/Bid/lists/DefaultList"
    },
    "first": {
      "href": "/DatacentrixBidManagement/entities/Bid/lists/DefaultList"
    }
  },
   "_embedded": {
    "DefaultList": [
      {
        "_links": {
          "item": {
            "href": "/DatacentrixBidManagement/entities/Bid/items/114690"
          }
        },
        "Tracking": {
          "CreatedDate": "2025-03-17T16:14:05Z"
        },
        "Properties": {
          "Bid_Title": "MS Dynamics RFP",
          "Bid_Description": "Detailed Description",
          "Bid_Budget": "350000.0000",
          "Bid_Status": "DRAFT",
          "Bid_OpportunitySource": "rfp",
          "Bid_BusinessNeed": "Business Need",
          "Bid_PotentialBenefits": "Potential Benefits",
          "Bid_PreliminaryScope": "Preliminary Scope",
          "Bid_Stakeholders": "Key Stakeholders",
          "Bid_RiskAssessment": "Initial Risk Assessment",
          "Bid_BidStrategy": "Bid Strategy",
          "Bid_CompetitiveAnalysis": "Competitive Analysis",
          "Bid_WinStrategy": "Win Strategy",
          "Bid_TechnicalApproach": "Technical Approach",
          "Bid_PricingStrategy": "Pricing Strategy",
          "Bid_QualityStandards": "Quality Standards",
          "Bid_ComplianceRequirements": "Compliance Requirements",
          "Bid_DueDate": "2025-03-17Z",
          "Bid_Deliverables": "Deliverables",
          "Bid_Milestones": "Key Milestones",
          "Bid_ResourceRequirements": "Resource Requirements",
          "Bid_StrategicAlignment": "expansion",
          "Bid_TeamComposition": "Team Composition"
        },
        "BusinessWorkspace": {
          "BusinessWorkspaceId": "262698"
        }
      }
    ]
  }
}
```

### RESPONSE Body EXAMPLE for multiple items that are retrieved (application/json):
```json
{
  "page": {
    "skip": 0,
    "top": 0,
    "count": 2
  },
  "_links": {
    "self": {
      "href": "/DatacentrixBidManagement/entities/Bid/lists/DefaultList"
    },
    "first": {
      "href": "/DatacentrixBidManagement/entities/Bid/lists/DefaultList"
    }
  },
  "_embedded": {
    "DefaultList": [
      {
        "_links": {
          "item": {
            "href": "/DatacentrixBidManagement/entities/Bid/items/114690"
          }
        },
        "Tracking": {
          "CreatedDate": "2025-03-17T16:14:05Z"
        },
        "Properties": {
          "Bid_Title": "MS Dynamics RFP",
          "Bid_Description": "Detailed Description",
          "Bid_Budget": "350000.0000",
          "Bid_Status": "DRAFT",
          "Bid_OpportunitySource": "rfp",
          "Bid_BusinessNeed": "Business Need",
          "Bid_PotentialBenefits": "Potential Benefits",
          "Bid_PreliminaryScope": "Preliminary Scope",
          "Bid_Stakeholders": "Key Stakeholders",
          "Bid_RiskAssessment": "Initial Risk Assessment",
          "Bid_BidStrategy": "Bid Strategy",
          "Bid_CompetitiveAnalysis": "Competitive Analysis",
          "Bid_WinStrategy": "Win Strategy",
          "Bid_TechnicalApproach": "Technical Approach",
          "Bid_PricingStrategy": "Pricing Strategy",
          "Bid_QualityStandards": "Quality Standards",
          "Bid_ComplianceRequirements": "Compliance Requirements",
          "Bid_DueDate": "2025-03-17Z",
          "Bid_Deliverables": "Deliverables",
          "Bid_Milestones": "Key Milestones",
          "Bid_ResourceRequirements": "Resource Requirements",
          "Bid_StrategicAlignment": "expansion",
          "Bid_TeamComposition": "Team Composition"
        },
        "BusinessWorkspace": {
          "BusinessWorkspaceId": "262698"
        }
      },
      {
        "_links": {
          "item": {
            "href": "/DatacentrixBidManagement/entities/Bid/items/114693"
          }
        },
        "Tracking": {
          "CreatedDate": "2025-03-18T15:46:41Z"
        },
        "Properties": {
          "Bid_Title": "sdf",
          "Bid_Description": "sdf",
          "Bid_Budget": "333333.0000",
          "Bid_Status": "DRAFT",
          "Bid_OpportunitySource": "rfp",
          "Bid_BusinessNeed": "fdsf",
          "Bid_PotentialBenefits": "sdf",
          "Bid_PreliminaryScope": "sdf",
          "Bid_Stakeholders": "sdf",
          "Bid_RiskAssessment": "sdf",
          "Bid_BidStrategy": "sdf",
          "Bid_CompetitiveAnalysis": "sdf",
          "Bid_WinStrategy": "sdf",
          "Bid_TechnicalApproach": "sdf",
          "Bid_PricingStrategy": "sdf",
          "Bid_QualityStandards": "sdf",
          "Bid_ComplianceRequirements": "sdf",
          "Bid_DueDate": null,
          "Bid_Deliverables": null,
          "Bid_Milestones": null,
          "Bid_ResourceRequirements": null,
          "Bid_StrategicAlignment": null,
          "Bid_TeamComposition": null
        },
        "BusinessWorkspace": {
          "BusinessWorkspaceId": "262705"
        }
      }
    ]
  }
}
```

### RESPONSE CODES:
- 201: Success
- 400: Error
- 500: Error

### Notes:
Handles the queries with large, complex sets of parameters that can be too large to be passed as query parameters. The query parameters supported by this operation are listed below:

distinct - Boolean value (true or false) to indicate whether the result must only contain unique values.
skip - Offset of the first item to include in the results. This may be used to page through long result sets.
top - Number of items to include in the results. This may be used to limit the returned result set and when paging through long result sets.
parameters - One or more property conditions. Each property part has a name, and a comparison part. The comparison part has an operator and a value element. Following are the valid operators:
eq - Equal to the parameter value. For example,

      "comparison": {
        "value": "Gene Kim",
        "operator": "eq"
      }
ne - Not equal to the parameter value.

lt - Less than the parameter value.

gt - Greater than the parameter value.

ge - Greater than or equal to the parameter value.

le - Less than or equal to the parameter value.

Like - Like the parameter value, using the database server's wild card capabilities. For example, "value": "*Tolkien" returns items where the given property ends in "Tolkien".

IsNull - Null. This does not take a value. For example,

      "comparison": {
        "operator": "IsNull"
      }
IsNotNull - Not null. This does not take a value. For example,

      "comparison": {
        "operator": "IsNotNull"
      }
InList - In a specified array of possible values. For example,

      "comparison": {
        "values": [ "The Goal", "The Unicorn project" ],
        "operator": "IsList"
      }
can be used to find items where parameter values contain "The Goal" and "The Unicorn project"

NotInList - Not in a specified list of possible values.

Between - Between the specified bounds. For example,

      "comparison": {
        "from": "13",
        "to": "99",
        "operator": "Between"
      }
can be used to locate items with a value between 13 and 99. The range is inclusive; items with values of 13 or 99 are returned.

orderBy - Orders the result on a specific property. The name must contain the property name to order by and sort type can be ASC for ascending sort or DESC for descending sort, where ASC is the default. For example, orderby= <Property Name> ASC
select - Returns only specific properties and their value instead of all properties. For example,
      "select": [
        {
          "name": "<Property Name>",
        }
      ]
can be used to only return the given property data.
------------------------------------------------------- END OF Retrieve Bid items API ------------------

------------------------------------------------------- START OF Update Bid API ------------------

## Update an existing Bid item
URL: Method: PUT {NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/items/{id}

### REQUEST Headers:
Content-Type: application/json
SAMLart: {SAML assertion}

### REQUEST Body EXAMPLE (application/json):
```json
{
  "Properties": {
    "Bid_Title": "string",
    "Bid_Description": "string",
    "Bid_Budget": 0,
    "Bid_Status": "string",
    "Bid_OpportunitySource": "string",
    "Bid_BusinessNeed": "string",
    "Bid_PotentialBenefits": "string",
    "Bid_PreliminaryScope": "string",
    "Bid_Stakeholders": "string",
    "Bid_RiskAssessment": "string",
    "Bid_BidStrategy": "string",
    "Bid_CompetitiveAnalysis": "string",
    "Bid_WinStrategy": "string",
    "Bid_TechnicalApproach": "string",
    "Bid_PricingStrategy": "string",
    "Bid_QualityStandards": "string",
    "Bid_ComplianceRequirements": "string",
    "Bid_StrategicAlignment": "string",
    "Bid_TeamComposition": "string",
    "Bid_ResourceRequirements": "string",
    "Bid_Deliverables": "string",
    "Bid_Milestones": "string",
    "Bid_DueDate": "2025-03-17"
  }
}
```

### RESPONSE Body EXAMPLE (application/json):
NO RESPONSE BODY. ONLY RESPONSE CODE is returned.

### RESPONSE CODES:
- 204: Success
- 400: Error
- 404: Not Found
- 500: Error

### Notes:
The bidId in the URL must be a valid bid ID.
------------------------------------------------------- END OF Update Bid API ------------------
------------------------------------------------------- START OF READ Bid API ------------------
## READ an existing Bid item
URL: Method: GET {NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/items/{id}

### REQUEST Headers:
Content-Type: application/json
SAMLart: {SAML assertion}

### REQUEST Body EXAMPLE (application/json):
NO REQUEST BODY.

### RESPONSE Body EXAMPLE (application/json):
```json
{
  "Identity": {
    "Id": "114689",
    "ItemStatus": 1
  },
  "Properties": {
    "Bid_Title": "SAP ERP RFP",
    "Bid_Description": "Detailed Description",
    "Bid_Budget": "250000.0000",
    "Bid_Status": "DRAFT",
    "Bid_OpportunitySource": "rfp",
    "Bid_BusinessNeed": "Business Need",
    "Bid_PotentialBenefits": "Potential Benefits",
    "Bid_PreliminaryScope": "Preliminary Scope",
    "Bid_Stakeholders": "Key Stakeholders",
    "Bid_RiskAssessment": "Initial Risk Assessment",
    "Bid_BidStrategy": "Bid Strategy",
    "Bid_CompetitiveAnalysis": "Competitive Analysis",
    "Bid_WinStrategy": "Win Strategy",
    "Bid_TechnicalApproach": "Technical Approach",
    "Bid_PricingStrategy": "Pricing Strategy",
    "Bid_QualityStandards": "Quality Standards",
    "Bid_ComplianceRequirements": "Compliance Requirements",
    "Bid_StrategicAlignment": "core",
    "Bid_TeamComposition": "Team Composition",
    "Bid_ResourceRequirements": "Resource Requirements",
    "Bid_Deliverables": "Deliverables Deliverables",
    "Bid_Milestones": "Key Milestones",
    "Bid_DueDate": "2025-03-17Z"
  },
  "Title": {
    "Title": "Bid-114689"
  },
  "Tracking": {
    "LastModifiedDate": "2025-03-17T12:43:41Z",
    "CreatedDate": "2025-03-17T12:43:39Z"
  },
  "DynamicWorkflow": {},
  "TaskList": {},
  "BusinessWorkspace": {
    "BusinessWorkspaceId": "262801",
    "SynchronizationStatus": "Success",
    "SynchronizationError": null,
    "BusinessWorkspaceObjectId": "0050560A0173A1F08035DC22B526A1E4.114689",
    "CrossApplicationObjectId": null
  },
  "_links": {
    "self": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689"
    },
    "curies": [
      {
        "name": "DynamicWorkflow-actions",
        "href": "http://dbsapp:81/home/<USER>/app/entityservice/DatacentrixBidManagement"
      },
      {
        "name": "relationship",
        "href": "http://dbsapp:81/home/<USER>/app/entityservice/DatacentrixBidManagement"
      },
      {
        "name": "BusinessWorkspace-actions",
        "href": "http://dbsapp:81/home/<USER>/app/entityservice/DatacentrixBidManagement"
      },
      {
        "name": "OpenTextEntityIdentityComponents-relationship",
        "href": "http://dbsapp:81/home/<USER>/app/entityservice/OpenTextEntityIdentityComponents"
      }
    ],
    "DynamicWorkflow-actions:StartWorkflow": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/DynamicWorkflow/actions/StartWorkflow"
    },
    "relationship:DynamicWorkflows": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/childEntities/DynamicWorkflows"
    },
    "relationship:LifecycleTask": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/childEntities/LifecycleTask"
    },
    "BusinessWorkspace-actions:OpenWorkspace": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/BusinessWorkspace/actions/OpenWorkspace"
    },
    "BusinessWorkspace-actions:AddBusinessAttachment": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/BusinessWorkspace/actions/AddBusinessAttachment"
    },
    "BusinessWorkspace-actions:RemoveBusinessAttachment": {
      "href": "/DatacentrixBidManagement/entities/Bid/items/114689/BusinessWorkspace/actions/RemoveBusinessAttachment"
    },
    "OpenTextEntityIdentityComponents-relationship:CreatedBy": {
      "href": "/OpenTextEntityIdentityComponents/entities/User/items/16387"
    },
    "OpenTextEntityIdentityComponents-relationship:LastModifiedBy": {
      "href": "/OpenTextEntityIdentityComponents/entities/User/items/16387"
    }
  }
}
```

### RESPONSE CODES:
- 200: Success
- 400: Error
- 500: Error

### Notes:
The {id} in the URL must be a valid bid ID.
The 'BusinessWorkspaceId' is the ID of the business workspace associated with the bid. This will be required when uploading attachments to the bid.
------------------------------------------------------- END OF READ Bid API ------------------

------------------------------------------------------- START OF Upload Attachment API ------------------
# Upload Document API Info
Description: Upload a document to the bid. This is a SOAP API.
Method: Post
URL: {NEXT_PUBLIC_APPWORKS_BASE_URL}/com.eibus.web.soap.Gateway.wcp?SAMLart={{samlart}}
Headers:
  - Content-Type: text/xml

### Request Body Structure
```xml
<SOAP:Envelope xmlns:SOAP="http://schemas.xmlsoap.org/soap/envelope/">
  <SOAP:Body>
    <CreateDocument xmlns="http://schemas.cordys.com/documentstore/default/1.0">
      <DocumentName>document_name.pdf</DocumentName>
      <Description>Document description</Description>
      <DocumentContent isUrl="false">base64_encoded_content</DocumentContent>
      <Id></Id>
      <Folder>Other Documents</Folder>
      <BusinessWorkspaceId>{{businessworkspaceid}}</BusinessWorkspaceId>
      <IsMajorVersion>true</IsMajorVersion>
      <KeepPrivateToUser>false</KeepPrivateToUser>
      <Properties>
        <MimeType>application/pdf</MimeType>
      </Properties>
    </CreateDocument>
  </SOAP:Body>
</SOAP:Envelope>
```

### Request Parameters
- DocumentName: Name of the file (including extension)
- Description: Brief description of the document
- DocumentContent: Base64 encoded file content
- BusinessWorkspaceId: Obtained from Read Bid API
- MimeType: Document MIME type (e.g., pdf, jpg, png)

### Response Structure
```xml
<CreateDocumentResponse xmlns:SOAP="http://schemas.xmlsoap.org/soap/envelope/" xmlns="http://schemas.cordys.com/documentstore/default/1.0">
    <DocumentURL>APP82430/Folder1/PARAMETER</DocumentURL>
    <Id>53952</Id>
    <VersionInfo>
      <Version>
        <MajorVersion>0</MajorVersion>
        <MinorVersion>1</MinorVersion>
      </Version>
    </VersionInfo>
</CreateDocumentResponse>
```

### Response Parameters
- DocumentURL: URL where the document is stored
- Id: Unique identifier for the uploaded document
- VersionInfo: Document version details

## Error Handling
Common HTTP status codes:
- 200: Success
- 400: Bad Request (invalid parameters)
- 401: Unauthorized (invalid SAMLart)
- 500: Server Error

## Implementation Notes
1. Always base64 encode the file content before sending
2. Maximum file size: 10MB
3. Supported file types: PDF, JPG, PNG
4. Keep SAMLart token updated (usually valid for 30 minutes)
5. BusinessWorkspaceId must match the application

## Example Implementation Flow
1. Get application details using application ID
2. Extract BusinessWorkspaceId from response
3. Prepare document (base64 encode)
4. Construct SOAP request
5. Send upload request
6. Handle response/confirmation

------------------------------------------------------- END OF Upload Attachment API ------------------