import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

/**
 * Checks if the user is authenticated and redirects to the login page
 * if not. It also prevents logged in users from accessing the login page.
 */
export async function middleware(request: NextRequest) {
  // Get the user from the cookies
  const user = request.cookies.get('user');

  // List of public routes that don't require authentication
  const publicRoutes = ['/login', '/api/auth'];

  // Check if the current route is a public route
  const isPublicRoute = publicRoutes.some(route =>
    request.nextUrl.pathname.startsWith(route)
  );

  // If the user is not logged in and the route is not a public route
  // redirect to the login page with the current route as the "from" parameter
  if (!user && !isPublicRoute) {
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('from', request.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If the user is logged in and the current route is the login page
  // redirect to the root page
  if (user && request.nextUrl.pathname === '/login') {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If the user is logged in and the route is a public route
  // allow the request to continue
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
