---
description: 
globs: 
alwaysApply: true
---
description: Best practices for form handling with React Hook Form
globs: **/*.{ts,tsx,js,jsx}

- Use the `useForm` hook to manage form state and validation.
- Implement custom validation rules using the `register` function.
- Utilize the `Controller` component for integrating with UI libraries.
- Leverage the `watch` function for real-time form value monitoring.