---
description: 
globs: 
alwaysApply: true
---
description: Best practices for React component development
globs: **/*.{ts,tsx,js,jsx}

- Use functional components with hooks instead of class components.
- Keep components small and focused on a single responsibility.
- Utilize memoization techniques like `useMemo` and `useCallback` for performance optimization.
- Implement proper prop type validation using PropTypes or TypeScript.
