"use client";

import { useState, useEffect, useCallback } from 'react';
import * as IndexedDB from '@/lib/indexeddb';
import { DocumentVersion, DocumentComment } from '@/types';
import { useToast } from "@/components/ui/use-toast";

export function useIndexedDB(documentId?: string, bidId?: string) {
  const [isDBAvailable, setIsDBAvailable] = useState<boolean | null>(null);
  const [isOnline, setIsOnline] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [document, setDocument] = useState<any | null>(null);
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [comments, setComments] = useState<DocumentComment[]>([]);
  const { toast } = useToast();

  // Initialize and check IndexedDB availability
  useEffect(() => {
    const checkAvailability = () => {
      try {
        const available = IndexedDB.isIndexedDBAvailable();
        setIsDBAvailable(available);

        if (!available) {
          toast({
            title: "Limited functionality",
            description: "Your browser doesn't support offline document editing.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error checking IndexedDB availability:', error);
        setIsDBAvailable(false);
      }
    };

    checkAvailability();

    // Set up online/offline detection
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Back online",
        description: "Your changes will be synced to the server.",
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "You're offline",
        description: "Changes will be saved locally until you reconnect.",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Load document data when documentId changes
  useEffect(() => {
    if (documentId && isDBAvailable) {
      loadDocument(documentId);
      loadVersions(documentId);
      loadComments(documentId);
    }
  }, [documentId, isDBAvailable]);

  // Load document function
  const loadDocument = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const doc = await IndexedDB.getDocument(docId);
      
      // If bidId is provided, verify the document belongs to the bid
      if (doc && bidId) {
        if (doc.bidId && doc.bidId !== bidId) {
          console.warn(`Document ${docId} belongs to bid ${doc.bidId}, not ${bidId}`);
          setDocument(null);
          setIsLoading(false);
          return;
        }
        
        // If document doesn't have a bidId, update it
        if (!doc.bidId) {
          const updatedDoc = { ...doc, bidId };
          await IndexedDB.saveDocument(updatedDoc);
          setDocument(updatedDoc);
        } else {
          setDocument(doc);
        }
      } else {
        setDocument(doc);
      }
    } catch (error) {
      console.error("Error loading document:", error);
      toast({
        title: "Error",
        description: "Failed to load document from local storage.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast, bidId]);

  // Load versions function
  const loadVersions = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const docVersions = await IndexedDB.getDocumentVersions(docId);
      setVersions(docVersions);
    } catch (error) {
      console.error("Error loading document versions:", error);
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable]);

  // Load comments function
  const loadComments = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const docComments = await IndexedDB.getDocumentComments(docId);
      setComments(docComments);
    } catch (error) {
      console.error("Error loading document comments:", error);
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable]);

  // Save document function
  const saveDocument = useCallback(async (doc: any) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Make sure bidId is set if available
      if (bidId && !doc.bidId) {
        doc.bidId = bidId;
      }
      
      await IndexedDB.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Error",
        description: "Failed to save document to local storage.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast, bidId]);

  // Save document content
  const saveDocumentContent = useCallback(async (docId: string, content: string) => {
    if (!isDBAvailable || !docId) return null;
    
    setIsLoading(true);
    try {
      // Get existing document or create a new one
      let doc = await IndexedDB.getDocument(docId);
      
      if (!doc) {
        doc = {
          id: docId,
          bidId: bidId, // Set bidId for new documents
          content,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      } else {
        doc.content = content;
        doc.updatedAt = new Date().toISOString();
        
        // Update bidId if needed
        if (bidId && !doc.bidId) {
          doc.bidId = bidId;
        }
      }
      
      // Save unsaved edit if offline
      if (!isOnline) {
        await IndexedDB.saveDocumentEdit({
          id: crypto.randomUUID(),
          documentId: docId,
          content,
          timestamp: new Date().toISOString()
        });
        
        toast({
          title: "Saved offline",
          description: "Your changes are saved locally and will sync when you're back online.",
        });
      }
      
      await IndexedDB.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document content:", error);
      toast({
        title: "Error",
        description: "Failed to save document content locally.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, isOnline, toast, bidId]);

  // Create a new document version
  const createVersion = useCallback(async (params: {
    documentId: string;
    content: string;
    commitMessage: string;
    userId: string;
    userName: string;
    userRole?: string;
  }) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Get latest version to use as previous version
      const previousVersion = await IndexedDB.getLatestVersion(params.documentId);
      
      // Create version control service instance
      const { DocumentVersionControl } = await import('@/lib/documents');
      const versionControl = new DocumentVersionControl();
      
      // Create the new version
      const newVersion = await versionControl.createVersion({
        ...params,
        previousVersion
      });
      
      // Save to IndexedDB
      await IndexedDB.saveDocumentVersion(newVersion);
      
      // Update versions state
      setVersions(prev => [newVersion, ...prev]);
      
      toast({
        title: "Version created",
        description: `Created version ${newVersion.version}`,
      });
      
      return newVersion;
    } catch (error) {
      console.error("Error creating version:", error);
      toast({
        title: "Error",
        description: "Failed to create document version.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast]);

  // Add a comment to a document or version
  const addComment = useCallback(async (comment: Omit<DocumentComment, 'id' | 'createdAt'>) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      const newComment: DocumentComment = {
        ...comment,
        id: crypto.randomUUID(),
        createdAt: new Date().toISOString()
      };
      
      await IndexedDB.saveDocumentComment(newComment);
      
      // Update comments state
      setComments(prev => [newComment, ...prev]);
      
      return newComment;
    } catch (error) {
      console.error("Error adding comment:", error);
      toast({
        title: "Error",
        description: "Failed to add comment.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast]);

  // Restore a version
  const restoreVersion = useCallback(async (version: DocumentVersion, userId: string, userName: string, userRole?: string) => {
    if (!isDBAvailable || !version) return null;
    
    setIsLoading(true);
    try {
      // Create version control service instance
      const { DocumentVersionControl } = await import('@/lib/documents');
      const versionControl = new DocumentVersionControl();
      
      // Create a new version that reverts to the selected version
      const newVersion = await versionControl.revertToVersion({
        documentId: version.documentId,
        targetVersion: version,
        commitMessage: `Reverted to version ${version.version}`,
        userId,
        userName,
        userRole
      });
      
      // Save to IndexedDB
      await IndexedDB.saveDocumentVersion(newVersion);
      
      // Update document content
      await saveDocumentContent(version.documentId, version.content);
      
      // Update versions state
      setVersions(prev => [newVersion, ...prev]);
      
      toast({
        title: "Version restored",
        description: `Restored to version ${version.version}`,
      });
      
      return newVersion;
    } catch (error) {
      console.error("Error restoring version:", error);
      toast({
        title: "Error",
        description: "Failed to restore version.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, saveDocumentContent, toast]);

  // Sync offline changes when coming back online
  useEffect(() => {
    const syncOfflineChanges = async () => {
      if (isOnline && isDBAvailable && documentId) {
        try {
          const pendingEdits = await IndexedDB.getDocumentEdits(documentId);
          
          if (pendingEdits.length > 0) {
            toast({
              title: "Syncing changes",
              description: `Syncing ${pendingEdits.length} offline edits...`,
            });
            
            // Apply each edit in sequence (in a real app, would send to server)
            for (const edit of pendingEdits) {
              // Apply edit logic would go here
              // For example, calling an API to sync the change
              console.log('Syncing edit:', edit);
            }
            
            // Clear pending edits after successful sync
            await IndexedDB.clearDocumentEdits(documentId);
            
            toast({
              title: "Sync complete",
              description: "All offline changes have been synced.",
            });
          }
        } catch (error) {
          console.error("Error syncing offline changes:", error);
          toast({
            title: "Sync error",
            description: "Failed to sync some offline changes.",
            variant: "destructive",
          });
        }
      }
    };
    
    syncOfflineChanges();
  }, [isOnline, isDBAvailable, documentId, toast]);

  return {
    isDBAvailable,
    isOnline,
    isLoading,
    document,
    versions,
    comments,
    saveDocument,
    saveDocumentContent,
    createVersion,
    addComment,
    restoreVersion,
    loadDocument,
    loadVersions,
    loadComments,
  };
}
