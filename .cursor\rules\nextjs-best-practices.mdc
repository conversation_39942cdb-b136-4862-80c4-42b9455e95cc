---
description: 
globs: 
alwaysApply: true
---
description: Best practices for Next.js applications and routing
globs: **/*.{ts,tsx}

- Use the App Router for better performance and improved data fetching.
- Implement proper error boundaries to handle errors gracefully.
- Utilize Next.js built-in optimizations like image optimization and code splitting.
- Use `getStaticProps` and `getServerSideProps` appropriately for data fetching.