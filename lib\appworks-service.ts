import axios from 'axios';

/**
 * Service to handle Appworks API interactions for document management
 */
export class AppworksDocumentService {
  /**
   * Get a bid's BusinessWorkspaceId by bid ID
   * @param bidId The ID of the bid
   * @param samlToken The SAML token for authentication
   * @returns The BusinessWorkspaceId or null if not found
   */
  static async getBidBusinessWorkspaceId(bidId: string, samlToken: string): Promise<string | null> {
    try {
      console.log(`Getting BusinessWorkspaceId for bid ${bidId}`);
      
      // Create an axios instance with maxRedirects set to 0 to prevent redirect loops
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/items/${bidId}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'SAMLart': samlToken
          },
          maxRedirects: 0, // Prevent redirect loops
          validateStatus: function (status) {
            return status >= 200 && status < 400; // Only treat 2xx and 3xx as success
          }
        }
      );

      // Extract the BusinessWorkspaceId from the response
      const businessWorkspaceId = response.data?.BusinessWorkspace?.BusinessWorkspaceId;
      
      if (!businessWorkspaceId) {
        console.error('BusinessWorkspaceId not found in bid data', response.data);
        return null;
      }
      
      return businessWorkspaceId;
    } catch (error) {
      console.error('Error getting bid BusinessWorkspaceId:', error);
      throw error;
    }
  }

  /**
   * Upload a document to Appworks via SOAP API
   * @param params Upload parameters
   * @returns The document ID from the response
   */
  static async uploadDocument(params: {
    fileName: string;
    description: string;
    content: string; // Base64 encoded content
    mimeType: string;
    businessWorkspaceId: string;
    samlToken: string;
  }): Promise<string | null> {
    const { fileName, description, mimeType, businessWorkspaceId, samlToken } = params;
    
    // Properly process the content based on its format
    let processedContent = params.content;
    
    // If the content starts with a data URI prefix, remove it
    if (processedContent.startsWith('data:')) {
      processedContent = processedContent.split(',')[1];
    }
    
    // Ensure the Base64 string is properly formatted (no line breaks, etc.)
    processedContent = processedContent.replace(/\s/g, '');
    
    // Ensure Base64 padding is correct
    while (processedContent.length % 4 !== 0) {
      processedContent += '=';
    }
    
    console.log(`Uploading document: ${fileName}, Type: ${mimeType}, Size: ${processedContent.length} chars`);

    // Create SOAP envelope
    const soapEnvelope = `
      <SOAP:Envelope xmlns:SOAP="http://schemas.xmlsoap.org/soap/envelope/">
        <SOAP:Body>
          <CreateDocument xmlns="http://schemas.cordys.com/documentstore/default/1.0">
            <DocumentName>${fileName}</DocumentName>
            <Description>${description}</Description>
            <DocumentContent isUrl="false">${processedContent}</DocumentContent>
            <Id></Id>
            <Folder>Other Documents</Folder>
            <BusinessWorkspaceId>${businessWorkspaceId}</BusinessWorkspaceId>
            <IsMajorVersion>true</IsMajorVersion>
            <KeepPrivateToUser>false</KeepPrivateToUser>
            <Properties>
              <MimeType>${mimeType}</MimeType>
            </Properties>
          </CreateDocument>
        </SOAP:Body>
      </SOAP:Envelope>
    `;

    try {
      console.log('Sending SOAP request to Appworks API...');
      
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_APPWORKS_BASE_URL}/com.eibus.web.soap.Gateway.wcp?SAMLart=${samlToken}`,
        soapEnvelope,
        {
          headers: {
            'Content-Type': 'text/xml',
            'SOAPAction': 'CreateDocument'
          }
        }
      );

      // Parse the XML response to extract document ID
      const responseText = response.data;
      console.log('SOAP Response received.');
      
      const idMatch = responseText.match(/<Id>(\d+)<\/Id>/);
      
      if (idMatch && idMatch[1]) {
        console.log(`Document uploaded successfully with ID: ${idMatch[1]}`);
        return idMatch[1];
      }
      
      console.error('Document ID not found in response', responseText);
      return null;
    } catch (error) {
      console.error('Error uploading document:', error);
      throw error;
    }
  }

  /**
   * Helper function to convert text content to PDF (mock implementation)
   * In a real application, you would use a proper HTML-to-PDF library
   * @param content HTML or text content
   * @returns Base64 encoded PDF
   */
  static async convertToPdf(content: string): Promise<string> {
    // In a real implementation, you would use a library like jsPDF or call a service
    // For this example, we'll just return a mock base64 string
    console.log('Converting content to PDF (mock implementation)');
    
    // This is just a minimal PDF in base64 - in a real app you'd generate a proper PDF
    return 'JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PAovRmlsdGVyIC9GbGF0ZURlY29kZQovTGVuZ3RoIDM4Cj4+CnN0cmVhbQp4nCvkMlAwUDC1NNUzMVGwMDHUszRSKErlCtfiyuMK5AIARP4GFgplbmRzdHJlYW0KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL01lZGlhQm94IFswIDAgNTk1LjMyIDg0MS45Ml0KL1Jlc291cmNlcyA8PAovRm9udCA8PAovRjEgMSAwIFIKL0YyIDIgMCBSCj4+Cj4+Ci9Db250ZW50cyA1IDAgUgovUGFyZW50IDMgMCBSCj4+CmVuZG9iagozIDAgb2JqCjw8Ci9UeXBlIC9QYWdlcwovQ291bnQgMQovS2lkcyBbNCAwIFJdCj4+CmVuZG9iagoyIDAgb2JqCjw8Ci9UeXBlIC9Gb250Ci9TdWJ0eXBlIC9UeXBlMQovQmFzZUZvbnQgL0hlbHZldGljYQo+PgplbmRvYmoKMSAwIG9iago8PAovVHlwZSAvRm9udAovU3VidHlwZSAvVHlwZTEKL0Jhc2VGb250IC9IZWx2ZXRpY2EtQm9sZAo+PgplbmRvYmoKNiAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCnhyZWYKMCA3CjAwMDAwMDAwMDAgNjU1MzUgZiAKMDAwMDAwMDM0MyAwMDAwMCBuIAowMDAwMDAwMjg2IDAwMDAwIG4gCjAwMDAwMDAyMjMgMDAwMDAgbiAKMDAwMDAwMDA5MCAwMDAwMCBuIAowMDAwMDAwMDE1IDAwMDAwIG4gCjAwMDAwMDA0MTUgMDAwMDAgbiAKdHJhaWxlcgo8PAovU2l6ZSA3Ci9Sb290IDYgMCBSCi9JRCBbPDM3N2EyMTcxZmNiYmRiZWRkYjE1ZjA2OWJkZmIyOTQxPiA8Mzc3YTIxNzFmY2JiZGJlZGRiMTVmMDY5YmRmYjI5NDE+XQo+PgpzdGFydHhyZWYKNDY0CiUlRU9GCg==';
  }

  /**
   * Export document content as a downloadable file
   * @param documentTitle Title of the document (used for filename)
   * @param content HTML content of the document
   * @returns URL for downloading the file
   */
  static exportDocument(documentTitle: string, content: string): string {
    // Create a Blob from the content
    const blob = new Blob([content], { type: 'text/html' });
    
    // Create a URL for the Blob
    const url = URL.createObjectURL(blob);
    
    // Create a link element to trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = `${documentTitle.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
    
    // Append to body, click, and clean up
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    return url;
  }
}
