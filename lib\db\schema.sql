-- SQLite schema for document management system
PRAGMA foreign_keys = ON;

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id TEXT PRIMARY KEY,
    bidId TEXT,
    title TEXT,
    content TEXT,
    createdAt TEXT,
    updatedAt TEXT,
    createdBy TEXT,
    status TEXT DEFAULT 'draft',
    lastVersionId TEXT,
    lastVersion TEXT,
    metadata TEXT       -- Stored as JSON for flexible schema
);

-- Create indexes for documents table
CREATE INDEX IF NOT EXISTS idx_documents_bidId ON documents(bidId);
CREATE INDEX IF NOT EXISTS idx_documents_updatedAt ON documents(updatedAt);

-- Document versions table
CREATE TABLE IF NOT EXISTS document_versions (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    content TEXT,
    version TEXT,
    previousVersionId TEXT,
    diff TEXT,
    createdAt TEXT,
    createdBy_id TEXT,
    createdBy_name TEXT,
    createdBy_role TEXT,
    commitMessage TEXT,
    status TEXT,
    FOREIGN KEY (documentId) REFERENCES documents(id)
);

-- Create indexes for document_versions table
CREATE INDEX IF NOT EXISTS idx_versions_documentId ON document_versions(documentId);
CREATE INDEX IF NOT EXISTS idx_versions_version ON document_versions(version);
CREATE INDEX IF NOT EXISTS idx_versions_createdAt ON document_versions(createdAt);

-- Document comments table
CREATE TABLE IF NOT EXISTS document_comments (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    versionId TEXT,
    content TEXT,
    author_id TEXT,
    author_name TEXT,
    author_avatar TEXT,
    createdAt TEXT,
    selection_from INTEGER,
    selection_to INTEGER,
    resolved BOOLEAN DEFAULT 0,
    parent_comment_id TEXT,
    FOREIGN KEY (documentId) REFERENCES documents(id),
    FOREIGN KEY (versionId) REFERENCES document_versions(id),
    FOREIGN KEY (parent_comment_id) REFERENCES document_comments(id)
);

-- Create indexes for document_comments table
CREATE INDEX IF NOT EXISTS idx_comments_documentId ON document_comments(documentId);
CREATE INDEX IF NOT EXISTS idx_comments_versionId ON document_comments(versionId);
CREATE INDEX IF NOT EXISTS idx_comments_createdAt ON document_comments(createdAt);
CREATE INDEX IF NOT EXISTS idx_comments_parent ON document_comments(parent_comment_id);

-- Document edits table (for offline edits)
CREATE TABLE IF NOT EXISTS document_edits (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    content TEXT,
    timestamp TEXT,
    FOREIGN KEY (documentId) REFERENCES documents(id)
);

-- Create indexes for document_edits table
CREATE INDEX IF NOT EXISTS idx_edits_documentId ON document_edits(documentId);
CREATE INDEX IF NOT EXISTS idx_edits_timestamp ON document_edits(timestamp); 