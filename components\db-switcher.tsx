"use client";

import { useState } from "react";
import { useDB } from "@/providers/db-provider";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Database, HardDrive } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";

export function DBSwitcher() {
  const db = useDB();
  const { toast } = useToast();
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  const handleToggle = async (checked: boolean) => {
    setIsTransitioning(true);
    
    try {
      const newProvider = checked ? 'sqlite' : 'indexeddb';
      
      // Add a warning if switching away from SQLite
      if (newProvider === 'indexeddb') {
        toast({
          title: 'Warning: Switching to IndexedDB',
          description: 'SQLite is the recommended database provider for better performance and reliability.',
          variant: 'destructive',
        });
      }
      
      db.setProvider(newProvider);
      
      toast({
        title: `Database changed to ${newProvider}`,
        description: `Now using ${newProvider === 'sqlite' ? 'SQLite' : 'IndexedDB'} for offline storage.`,
      });
    } catch (error) {
      console.error("Error switching database provider:", error);
      toast({
        title: "Error switching database",
        description: "Failed to switch database provider. Check console for details.",
        variant: "destructive",
      });
    } finally {
      setIsTransitioning(false);
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Database className="mr-2 h-5 w-5" />
          Database Provider
        </CardTitle>
        <CardDescription>
          Choose which database to use for offline storage
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex flex-col space-y-1">
            <div className="flex items-center">
              <HardDrive className="mr-2 h-4 w-4" />
              <Label htmlFor="db-provider" className="font-medium">
                Use SQLite
              </Label>
              <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 hover:bg-green-50 border-green-200">Recommended</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              SQLite provides better performance and reliability
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={db.provider === 'indexeddb' ? "default" : "outline"}>
              IndexedDB
            </Badge>
            <Switch
              id="db-provider"
              checked={db.provider === 'sqlite'}
              onCheckedChange={handleToggle}
              disabled={isTransitioning}
            />
            <Badge variant={db.provider === 'sqlite' ? "default" : "outline"}>
              SQLite
            </Badge>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between text-xs text-muted-foreground">
        <div>Current provider: {db.provider}</div>
        <div>Available: {db.isAvailable ? "Yes" : "No"}</div>
      </CardFooter>
    </Card>
  );
} 