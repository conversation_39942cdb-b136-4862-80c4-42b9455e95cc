import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Trash2, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { useToast } from "@/components/ui/use-toast";
import * as IndexedDB from '@/lib/indexeddb';

enum ClearStatus {
  IDLE = 'idle',
  CLEARING = 'clearing',
  SUCCESS = 'success',
  ERROR = 'error'
}

export function ClearIndexedDB() {
  const [status, setStatus] = useState<ClearStatus>(ClearStatus.IDLE);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const clearDatabase = async () => {
    try {
      setStatus(ClearStatus.CLEARING);
      setError(null);
      
      await IndexedDB.clearIndexedDB();
      
      setStatus(ClearStatus.SUCCESS);
      toast({
        title: "IndexedDB cleared",
        description: "All IndexedDB data has been successfully cleared.",
      });
      
      // Reset status after 3 seconds
      setTimeout(() => {
        setStatus(ClearStatus.IDLE);
      }, 3000);
    } catch (err: any) {
      setStatus(ClearStatus.ERROR);
      setError(err.message || "An error occurred while clearing IndexedDB");
      toast({
        title: "Error clearing IndexedDB",
        description: err.message || "An error occurred while clearing IndexedDB",
        variant: "destructive",
      });
    }
  };

  const renderStatus = () => {
    switch (status) {
      case ClearStatus.CLEARING:
        return (
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertTitle>Clearing data</AlertTitle>
            <AlertDescription>
              Clearing all data from IndexedDB...
            </AlertDescription>
          </Alert>
        );
      case ClearStatus.SUCCESS:
        return (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>
              All IndexedDB data has been successfully cleared.
            </AlertDescription>
          </Alert>
        );
      case ClearStatus.ERROR:
        return (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "An error occurred while clearing IndexedDB"}
            </AlertDescription>
          </Alert>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5" /> Clear IndexedDB
        </CardTitle>
        <CardDescription>
          Clear all data from IndexedDB and use SQLite going forward
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          This will permanently delete all data stored in IndexedDB. Your documents will still be accessible from SQLite database if they have been migrated.
        </p>
        
        {renderStatus()}
      </CardContent>
      <CardFooter>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" disabled={status === ClearStatus.CLEARING}>
              {status === ClearStatus.CLEARING && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Clear IndexedDB
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will permanently delete all data stored in IndexedDB. This action cannot be undone.
                <br /><br />
                <strong>Before proceeding, make sure your data has been migrated to SQLite.</strong>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={clearDatabase}>
                Yes, clear IndexedDB
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardFooter>
    </Card>
  );
} 