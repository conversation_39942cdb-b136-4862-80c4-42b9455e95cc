import { NextResponse } from 'next/server';
import axios from 'axios';
import { cookies } from 'next/headers';
import { getSAMLArtifact } from '@/lib/samlUtils';

/**
 * GET handler for /api/bids
 * Fetches bids from Appworks API and returns them in our app's format
 */
export async function GET() {
  try {
    // Get SAML artifact from cookie if possible (server-side)
    // If not available, client will need to handle auth
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get('appSession');
    let samlArtifact = null;
    
    if (sessionCookie?.value) {
      try {
        const sessionData = JSON.parse(sessionCookie.value);
        if (sessionData?.user?.ticket) {
          // If we have a session, we'll use it to validate with Appworks
          const response = await axios.post(
            process.env.NEXT_PUBLIC_APPWORKS_AUTH_URL!,
            // SOAP envelope for authentication
            `<SOAP:Envelope xmlns:SOAP="http://schemas.xmlsoap.org/soap/envelope/">
              <SOAP:Header>
                <OTAuthentication xmlns="urn:api.bpm.opentext.com">
                  <AuthenticationToken>${sessionData.user.ticket}</AuthenticationToken>
                </OTAuthentication>
              </SOAP:Header>
              <SOAP:Body>
                <samlp:Request xmlns:samlp="urn:oasis:names:tc:SAML:1.0:protocol" 
                              MajorVersion="1" 
                              MinorVersion="1" 
                              IssueInstant="${new Date().toISOString()}" 
                              RequestID="${Date.now()}">
                  <samlp:AuthenticationQuery>
                    <saml:Subject xmlns:saml="urn:oasis:names:tc:SAML:1.0:assertion">
                      <saml:NameIdentifier Format="urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"/>
                    </saml:Subject>
                  </samlp:AuthenticationQuery>
                </samlp:Request>
              </SOAP:Body>
            </SOAP:Envelope>`,
            {
              headers: {
                'Content-Type': 'text/xml',
                'SOAPAction': 'urn:oasis:names:tc:SAML:1.0:protocol:AuthenticationQuery'
              }
            }
          );
          
          // Extract SAML artifact from response
          const responseData = response.data;
          if (typeof responseData === 'string') {
            const match = responseData.match(/<samlp:AssertionArtifact[^>]*>([^]*?)<\/samlp:AssertionArtifact>/);
            if (match && match[1]) {
              samlArtifact = match[1].trim();
            }
          }
        }
      } catch (e) {
        console.error('Error processing session cookie:', e);
      }
    }

    // If no artifact from server-side auth, return error
    if (!samlArtifact) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Now make the actual request to Appworks API
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/lists/DefaultList`,
      {
        distinct: 0,
        skip: 0,
        top: 100,
        parameters: {}, // Empty to get all bids
        orderBy: [
          {
            name: 'Properties.Bid_Title',
            sortType: 'ASC'
          }
        ],
        select: []
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'SAMLart': samlArtifact
        }
      }
    );

    // Transform the response data to match our app's format
    const bids = transformAppworksBids(response.data);
    
    return NextResponse.json({ bids });
  } catch (error: any) {
    console.error('Error fetching bids from Appworks:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch bids', 
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * Transforms Appworks bid data to our application's format
 */
function transformAppworksBids(response: any) {
  if (!response._embedded?.DefaultList) {
    return [];
  }

  return response._embedded.DefaultList.map((item: any) => {
    // Extract the ID from the href
    const hrefParts = item._links.item.href.split('/');
    const bidId = hrefParts[hrefParts.length - 1];

    // Parse the bid data
    const bidData = item.Properties;
    
    return {
      bidId,
      title: bidData.Bid_Title || 'Untitled Bid',
      description: bidData.Bid_Description || '',
      budget: parseFloat(bidData.Bid_Budget) || 0,
      status: bidData.Bid_Status || 'DRAFT',
      dueDate: '', // Not available in the Appworks data
      createdAt: new Date().toISOString(), // Not available in the Appworks data
      updatedAt: new Date().toISOString(), // Not available in the Appworks data
      createdBy: '', // Not available in the Appworks data
      assignedTo: [], // Not available in the Appworks data
      documents: [], // Not available in the Appworks data
      comments: [], // Not available in the Appworks data
      // Add additional Appworks-specific fields
      opportunitySource: bidData.Bid_OpportunitySource || '',
      businessNeed: bidData.Bid_BusinessNeed || '',
      strategicAlignment: bidData.Bid_StrategicAlignment || '',
      potentialBenefits: bidData.Bid_PotentialBenefits || '',
      preliminaryScope: bidData.Bid_PreliminaryScope || '',
      stakeholders: bidData.Bid_Stakeholders || '',
      riskAssessment: bidData.Bid_RiskAssessment || '',
      bidStrategy: bidData.Bid_BidStrategy || '',
      competitiveAnalysis: bidData.Bid_CompetitiveAnalysis || '',
      winStrategy: bidData.Bid_WinStrategy || '',
      technicalApproach: bidData.Bid_TechnicalApproach || '',
      pricingStrategy: bidData.Bid_PricingStrategy || '',
      qualityStandards: bidData.Bid_QualityStandards || '',
      complianceRequirements: bidData.Bid_ComplianceRequirements || ''
    };
  });
}
