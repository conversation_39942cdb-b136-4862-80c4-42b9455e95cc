---
description: 
globs: 
alwaysApply: true
---
description: Best practices for using Recharts for data visualization
globs: **/*.{ts,tsx}

- Use Recharts' responsive container for adaptive chart sizing.
- Implement custom shapes and labels for more control over chart appearance.
- Utilize Recharts' built-in animations for smooth data transitions.
- Optimize performance by using memoization and lazy loading of chart components.