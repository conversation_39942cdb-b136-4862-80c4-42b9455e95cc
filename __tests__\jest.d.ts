// Global Jest functions
declare function describe(name: string, fn: () => void): void;
declare function beforeAll(fn: () => void | Promise<void>): void;
declare function afterAll(fn: () => void | Promise<void>): void;
declare function beforeEach(fn: () => void | Promise<void>): void;
declare function afterEach(fn: () => void | Promise<void>): void;
declare function test(name: string, fn: () => void | Promise<void>, timeout?: number): void;
declare function expect(value: any): {
  toBe(expected: any): void;
  toEqual(expected: any): void;
  not: {
    toBeNull(): void;
    toBeUndefined(): void;
    toBe(expected: any): void;
    toEqual(expected: any): void;
  };
  toBeNull(): void;
  toBeUndefined(): void;
};

// Jest globals
declare const jest: {
  fn: <T = any>() => jest.Mock<T>;
  mock: (moduleName: string, factory?: any) => jest.Mock;
};

declare namespace jest {
  interface Mock<T = any> {
    (... args: any[]): T;
    mockReturnValue: (val: T) => Mock<T>;
    mockImplementation: (fn: Function) => Mock;
  }
} 