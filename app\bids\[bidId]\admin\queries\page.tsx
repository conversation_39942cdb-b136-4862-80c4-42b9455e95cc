'use client';

import { useParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Loader2, MessageCircle, Clock, CheckCircle2, Filter } from 'lucide-react';
import { useState } from 'react';

const responseSchema = z.object({
  message: z.string().min(20, 'Response must be at least 20 characters'),
});

type ResponseForm = z.infer<typeof responseSchema>;

interface Query {
  id: string;
  bidId: string;
  vendorId: string;
  subject: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  category: 'technical' | 'commercial' | 'compliance' | 'other';
  status: 'pending' | 'answered';
  createdAt: string;
  response?: {
    message: string;
    respondedAt: string;
    respondedBy: string;
  };
}

// TODO: Replace with actual API calls when admin query API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockQueries: Query[] = [
  {
    id: '1',
    bidId: '123',
    vendorId: 'v1',
    subject: 'Technical Specification Clarification',
    message: 'Could you please clarify the requirements for the API integration mentioned in section 3.2?',
    priority: 'high',
    category: 'technical',
    status: 'answered',
    createdAt: '2025-03-13T14:00:00Z',
    response: {
      message: 'The API integration should follow REST principles and support OAuth 2.0 authentication...',
      respondedAt: '2025-03-13T16:00:00Z',
      respondedBy: 'John Doe',
    },
  },
  {
    id: '2',
    bidId: '123',
    vendorId: 'v1',
    subject: 'Pricing Model Question',
    message: 'Is it acceptable to propose a subscription-based pricing model instead of a perpetual license?',
    priority: 'medium',
    category: 'commercial',
    status: 'pending',
    createdAt: '2025-03-14T09:00:00Z',
  },
];

export default function AdminQueriesPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [queries, setQueries] = useState<Query[]>(mockQueries);
  const [selectedQuery, setSelectedQuery] = useState<Query | null>(null);
  const [filters, setFilters] = useState({
    status: 'all',
    priority: 'all',
    category: 'all',
  });

  const form = useForm<ResponseForm>({
    resolver: zodResolver(responseSchema),
    defaultValues: {
      message: '',
    },
  });

  const handleSubmitResponse = async (data: ResponseForm) => {
    if (!selectedQuery) return;

    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit response
      const updatedQuery: Query = {
        ...selectedQuery,
        status: 'answered',
        response: {
          message: data.message,
          respondedAt: new Date().toISOString(),
          respondedBy: 'John Doe', // TODO: Get from auth context
        },
      };

      setQueries((prev) =>
        prev.map((q) => (q.id === selectedQuery.id ? updatedQuery : q))
      );

      toast({
        title: "Response Submitted",
        description: "Your response has been sent to the vendor.",
      });

      form.reset();
      setSelectedQuery(null);
    } catch (error) {
      console.error('Error submitting response:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const filteredQueries = queries.filter((query) => {
    if (filters.status !== 'all' && query.status !== filters.status) return false;
    if (filters.priority !== 'all' && query.priority !== filters.priority) return false;
    if (filters.category !== 'all' && query.category !== filters.category) return false;
    return true;
  });

  const getPriorityColor = (priority: Query['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getCategoryColor = (category: Query['category']) => {
    switch (category) {
      case 'technical':
        return 'bg-blue-500';
      case 'commercial':
        return 'bg-purple-500';
      case 'compliance':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Manage Vendor Queries</CardTitle>
              <CardDescription>
                Review and respond to vendor queries for bid #{bidId}.
              </CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4" />
                <span className="text-sm font-medium">Filters:</span>
              </div>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters((prev) => ({ ...prev, status: value }))}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="answered">Answered</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filters.priority}
                onValueChange={(value) => setFilters((prev) => ({ ...prev, priority: value }))}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filters.category}
                onValueChange={(value) => setFilters((prev) => ({ ...prev, category: value }))}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                  <SelectItem value="compliance">Compliance</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[600px] pr-4">
            <div className="space-y-4">
              {filteredQueries.map((query) => (
                <Card key={query.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold">{query.subject}</h4>
                        <Badge
                          variant="secondary"
                          className={getPriorityColor(query.priority)}
                        >
                          {query.priority}
                        </Badge>
                        <Badge
                          variant="secondary"
                          className={getCategoryColor(query.category)}
                        >
                          {query.category}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500">
                        From Vendor {query.vendorId} • {new Date(query.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <Badge
                      variant={query.status === 'answered' ? 'default' : 'secondary'}
                      className="ml-2"
                    >
                      {query.status === 'answered' ? (
                        <CheckCircle2 className="h-4 w-4 mr-1" />
                      ) : (
                        <Clock className="h-4 w-4 mr-1" />
                      )}
                      {query.status}
                    </Badge>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm">{query.message}</p>
                  </div>
                  {query.response ? (
                    <div className="mt-4 pl-4 border-l-2 border-gray-200">
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <MessageCircle className="h-4 w-4" />
                        <span>Response from {query.response.respondedBy}</span>
                        <span>•</span>
                        <span>{new Date(query.response.respondedAt).toLocaleString()}</span>
                      </div>
                      <p className="mt-2 text-sm">{query.response.message}</p>
                    </div>
                  ) : (
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="outline"
                          className="mt-4"
                          onClick={() => setSelectedQuery(query)}
                        >
                          Respond to Query
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Respond to Query</DialogTitle>
                          <DialogDescription>
                            Provide a response to the vendor&apos;s query. This will be sent directly to the vendor.
                          </DialogDescription>
                        </DialogHeader>
                        <Form {...form}>
                          <form onSubmit={form.handleSubmit(handleSubmitResponse)} className="space-y-4">
                            <FormField
                              control={form.control}
                              name="message"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Response Message *</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Type your response here..."
                                      className="min-h-[150px]"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <Button
                              type="submit"
                              className="w-full"
                              disabled={isSubmitting}
                            >
                              {isSubmitting ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Submitting...
                                </>
                              ) : (
                                'Submit Response'
                              )}
                            </Button>
                          </form>
                        </Form>
                      </DialogContent>
                    </Dialog>
                  )}
                </Card>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
