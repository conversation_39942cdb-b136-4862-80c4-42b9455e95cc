"use client";

import { useState, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/components/ui/use-toast';
import { jsPDF } from 'jspdf';
// @ts-ignore - Importing jspdf-autotable as it doesn't have type definitions
import 'jspdf-autotable';
// @ts-ignore - Importing html2canvas as it doesn't have type definitions
import html2canvas from 'html2canvas';
import { getSAMLArtifact } from '@/lib/samlUtils';
import axios from 'axios';

// Type definitions
interface UploadParams {
  fileName: string;
  description: string;
  content: string;
  mimeType: string;
  convertToPdf?: boolean;
}

/**
 * Custom hook for interacting with Appworks document APIs
 * Handles the workflow of getting BusinessWorkspaceId and uploading documents
 */
export function useAppworksDocument(bidId?: string) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [businessWorkspaceId, setBusinessWorkspaceId] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  /**
   * Get the BusinessWorkspaceId for a specific bid
   */
  const fetchBusinessWorkspaceId = useCallback(async (targetBidId?: string) => {
    if (!user) {
      setError("Authentication required");
      toast({
        title: "Authentication required",
        description: "You must be logged in to access bid workspace information.",
        variant: "destructive",
      });
      return null;
    }

    // Get the SAML artifact from storage
    const samlArtifact = getSAMLArtifact();
    if (!samlArtifact) {
      setError("Authentication token is missing");
      toast({
        title: "Authentication error",
        description: "Authentication token is missing. Please log in again.",
        variant: "destructive",
      });
      return null;
    }

    const bidToUse = targetBidId || bidId;
    if (!bidToUse) {
      setError("Bid ID is required");
      toast({
        title: "Missing information",
        description: "A bid ID is required to access the workspace.",
        variant: "destructive",
      });
      return null;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      console.log(`Fetching BusinessWorkspaceId for bid: ${bidToUse}`);
      
      // Make the request to our API endpoint that proxies to Appworks
      const response = await axios.get(`/api/bids/${bidToUse}/workspace`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `SAMLart ${samlArtifact.value}`
        },
        timeout: 10000 // 10 second timeout
      });

      // Extract the BusinessWorkspaceId from the response
      const workspaceId = response.data?.businessWorkspaceId;
      
      if (!workspaceId) {
        console.error('BusinessWorkspaceId not found in response', response.data);
        setError("BusinessWorkspaceId not found in response");
        return null;
      }

      console.log(`Successfully retrieved BusinessWorkspaceId: ${workspaceId}`);
      setBusinessWorkspaceId(workspaceId);
      return workspaceId;
    } catch (error) {
      // Log the error details
      console.error('Error fetching BusinessWorkspaceId:', error);
      
      let errorMessage = "Failed to fetch workspace information.";
      
      if (axios.isAxiosError(error)) {
        // Handle specific axios errors
        if (error.code === 'ECONNABORTED') {
          errorMessage = "Request timed out. Please try again.";
        } else if (error.code === 'ERR_FR_TOO_MANY_REDIRECTS') {
          errorMessage = "Authentication error. Please log in again.";
        } else if (error.response) {
          // Server responded with a non-2xx status
          errorMessage = `Server error: ${error.response.data?.error || error.message}`;
        } else if (error.request) {
          // Request was made but no response received
          errorMessage = "No response from server. Please check your connection.";
        }
      }
      
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [bidId, toast, user]);

  /**
   * Upload a document to Appworks
   */
  const uploadDocument = useCallback(async (params: UploadParams) => {
    // First ensure we have a BusinessWorkspaceId
    let workspaceId = businessWorkspaceId;
    
    if (!workspaceId && bidId) {
      console.log("No BusinessWorkspaceId found, fetching it...");
      workspaceId = await fetchBusinessWorkspaceId(bidId);
    }
    
    if (!workspaceId) {
      console.error("Cannot upload document: BusinessWorkspaceId not available");
      toast({
        title: "Upload Error",
        description: "Could not determine the workspace for upload.",
        variant: "destructive",
      });
      return false;
    }
    
    // Get the SAML artifact for authentication
    const samlArtifact = getSAMLArtifact();
    if (!samlArtifact) {
      toast({
        title: "Authentication error",
        description: "Authentication token is missing. Please log in again.",
        variant: "destructive",
      });
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      let content = params.content;
      let mimeType = params.mimeType;
      let fileName = params.fileName;
      
      // If we need to convert HTML to PDF before uploading
      if (params.convertToPdf && params.mimeType.includes('html')) {
        try {
          console.log("Converting HTML to PDF...");
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = params.content;
          document.body.appendChild(tempDiv);
          
          const canvas = await html2canvas(tempDiv);
          document.body.removeChild(tempDiv);
          
          const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'px',
            format: 'a4'
          });
          
          // Calculate dimensions
          const imgData = canvas.toDataURL('image/png');
          const imgWidth = pdf.internal.pageSize.getWidth();
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          
          pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
          content = pdf.output('datauristring');
          mimeType = 'application/pdf';
          fileName = params.fileName.replace('.html', '.pdf');
          
          console.log("Successfully converted HTML to PDF");
        } catch (convError) {
          console.error("Error converting HTML to PDF:", convError);
          // Continue with HTML if conversion fails
          console.log("Continuing with HTML content");
        }
      }
      
      // Ensure the content is properly formatted for the SOAP API
      // If content starts with data URI scheme, keep only the Base64 part
      if (content.startsWith('data:')) {
        content = content.split(',')[1];
      }
      
      // Remove any whitespace from the Base64 string
      content = content.replace(/\s/g, '');
      
      // Ensure proper Base64 padding
      while (content.length % 4 !== 0) {
        content += '=';
      }
      
      // Upload the document to Appworks via our API proxy
      console.log(`Uploading document '${fileName}' to workspace ${workspaceId}`);
      
      const response = await axios.post('/api/documents/upload', {
        fileName: fileName,
        description: params.description,
        content: content,
        mimeType: mimeType,
        businessWorkspaceId: workspaceId
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `SAMLart ${samlArtifact.value}`
        },
        timeout: 30000 // 30 second timeout for larger uploads
      });
      
      console.log("Upload response:", response.data);
      
      if (response.data && response.data.id) {
        toast({
          title: "Document uploaded",
          description: `${fileName} has been uploaded successfully.`,
        });
        return true;
      } else {
        throw new Error("Invalid response from upload API");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      
      let errorMessage = "Failed to upload document. Please try again.";
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          errorMessage = "Upload timed out. The document may be too large.";
        } else if (error.response) {
          // Check for SOAP fault message
          if (error.response.data && typeof error.response.data === 'string' && 
              error.response.data.includes('SOAP:Fault')) {
            if (error.response.data.includes('byte array has incorrect ending byte')) {
              errorMessage = "The document content has incorrect Base64 encoding. Please try a different document format.";
            } else {
              errorMessage = `SOAP API error: ${error.response.data.match(/<faultstring[^>]*>(.*?)<\/faultstring>/)?.[1] || 'Unknown SOAP error'}`;
            }
          } else {
            errorMessage = `Upload error: ${error.response.data?.message || error.response.data?.error || error.message}`;
          }
        }
      }
      
      setError(errorMessage);
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [bidId, businessWorkspaceId, fetchBusinessWorkspaceId, toast]);

  return {
    businessWorkspaceId,
    isLoading,
    error,
    fetchBusinessWorkspaceId,
    uploadDocument
  };
}
