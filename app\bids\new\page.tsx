"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Calendar as CalendarIcon, Plus, X, Save, ArrowLeft, ArrowRight } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import Link from "next/link";
import { createBid, NewBidData } from "@/services/bidService";
import { useToast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { getSAMLArtifact } from "@/lib/samlUtils";

const formSchema = z.object({
  // Opportunity Identification
  title: z.string().min(2, "Title must be at least 2 characters").max(100),
  description: z.string().min(10, "Description must be at least 10 characters"),
  budget: z.number().min(0, "Budget must be a positive number"),
  dueDate: z.date(),
  opportunitySource: z.string().min(1, "Please select an opportunity source"),
  businessNeed: z.string().min(10, "Business need must be at least 10 characters"),
  strategicAlignment: z.string().min(1, "Please select strategic alignment"),
  potentialBenefits: z.string().min(10, "Potential benefits must be at least 10 characters"),
  preliminaryScope: z.string().min(10, "Preliminary scope must be at least 10 characters"),
  stakeholders: z.string().min(10, "Stakeholders must be at least 10 characters"),
  riskAssessment: z.string().min(10, "Risk assessment must be at least 10 characters"),
  
  // Bid Planning
  bidStrategy: z.string().min(10, "Bid strategy must be at least 10 characters"),
  winStrategy: z.string().min(10, "Win strategy must be at least 10 characters"),
  competitiveAnalysis: z.string().min(10, "Competitive analysis must be at least 10 characters"),
  technicalApproach: z.string().min(10, "Technical approach must be at least 10 characters"),
  pricingStrategy: z.string().min(10, "Pricing strategy must be at least 10 characters"),
  teamComposition: z.string().min(10, "Team composition must be at least 10 characters"),
  resourceRequirements: z.string().min(10, "Resource requirements must be at least 10 characters"),
  deliverables: z.string().min(10, "Deliverables must be at least 10 characters"),
  milestones: z.string().min(10, "Milestones must be at least 10 characters"),
  qualityStandards: z.string().min(10, "Quality standards must be at least 10 characters"),
  complianceRequirements: z.string().min(10, "Compliance requirements must be at least 10 characters"),
});

export default function NewBidPage() {
  const [currentTab, setCurrentTab] = useState("identification");
  const [formProgress, setFormProgress] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const { user } = useAuth();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // Opportunity Identification
      title: "",
      description: "",
      budget: 0,
      dueDate: new Date(),
      opportunitySource: "",
      businessNeed: "",
      strategicAlignment: "",
      potentialBenefits: "",
      preliminaryScope: "",
      stakeholders: "",
      riskAssessment: "",
      
      // Bid Planning
      bidStrategy: "",
      winStrategy: "",
      competitiveAnalysis: "",
      technicalApproach: "",
      pricingStrategy: "",
      teamComposition: "",
      resourceRequirements: "",
      deliverables: "",
      milestones: "",
      qualityStandards: "",
      complianceRequirements: "",
    },
  });

  // Calculate form progress based on filled fields
  const calculateProgress = (values: z.infer<typeof formSchema>) => {
    const totalFields = Object.keys(formSchema.shape).length;
    const filledFields = Object.entries(values).filter(([_, value]) => {
      return value !== "" && value !== 0 && value !== null;
    }).length;
    return (filledFields / totalFields) * 100;
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);
      
      // Get the SAML artifact from storage
      const samlArtifact = getSAMLArtifact();
      
      if (!samlArtifact || !samlArtifact.value) {
        toast({
          title: "Authentication Error",
          description: "Your session may have expired. Please log in again.",
          variant: "destructive",
        });
        return;
      }
      
      // Create the bid using the service
      const newBid = await createBid(values as NewBidData, samlArtifact.value);
      
      toast({
        title: "Bid Created",
        description: `Bid "${values.title}" has been created successfully.`,
      });
      
      // Redirect to the bids list
      router.push("/bids");
    } catch (error: any) {
      console.error("Error creating bid:", error);
      
      toast({
        title: "Error Creating Bid",
        description: error.message || "There was an error creating your bid. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const watchedValues = form.watch();
  
  useEffect(() => {
    setFormProgress(calculateProgress(watchedValues));
  }, [watchedValues]);

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Link href="/bids" className="flex items-center text-muted-foreground hover:text-primary mb-4">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Bids
        </Link>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Create New Bid</h1>
            <p className="text-muted-foreground mt-1">Complete all required information to create a new bid</p>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground mb-2">Completion Progress</div>
            <Progress value={formProgress} className="w-[200px]" />
          </div>
        </div>
      </div>

      <Card>
        <CardContent className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <Tabs 
                value={currentTab} 
                onValueChange={setCurrentTab}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger 
                    value="identification"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                  >
                    1. Opportunity Identification
                  </TabsTrigger>
                  <TabsTrigger 
                    value="planning"
                    className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                  >
                    2. Bid Planning
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="identification" className="space-y-6">
                  <div className="grid grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Opportunity Title*</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter opportunity title" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="opportunitySource"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Opportunity Source*</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select opportunity source" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="rfp">RFP/RFQ</SelectItem>
                              <SelectItem value="direct">Direct Customer Request</SelectItem>
                              <SelectItem value="market">Market Research</SelectItem>
                              <SelectItem value="internal">Internal Initiative</SelectItem>
                              <SelectItem value="partner">Partner Referral</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="budget"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Budget</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter estimated budget"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>Enter amount in USD</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dueDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Target Completion Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-full pl-3 text-left font-normal",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date < new Date() || date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="businessNeed"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Business Need</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe the business need or problem to be solved"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Detailed Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter detailed opportunity description"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="strategicAlignment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Strategic Alignment</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select strategic alignment" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="core">Core Business Growth</SelectItem>
                              <SelectItem value="expansion">Market Expansion</SelectItem>
                              <SelectItem value="innovation">Innovation Initiative</SelectItem>
                              <SelectItem value="operational">Operational Excellence</SelectItem>
                              <SelectItem value="customer">Customer Experience</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="stakeholders"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Key Stakeholders</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="List key stakeholders and their roles"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="preliminaryScope"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Preliminary Scope</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Define the preliminary scope of work"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="potentialBenefits"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Potential Benefits</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe potential benefits and expected outcomes"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="riskAssessment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Initial Risk Assessment</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Identify potential risks and mitigation strategies"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="planning" className="space-y-6">
                  <Alert className="mb-6">
                    <AlertTitle>Complete Opportunity Identification First</AlertTitle>
                    <AlertDescription>
                      We recommend completing the Opportunity Identification section before moving to Bid Planning.
                    </AlertDescription>
                  </Alert>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="bidStrategy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bid Strategy</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Define the overall bid strategy"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="winStrategy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Win Strategy</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Outline the strategy to win this bid"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="competitiveAnalysis"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Competitive Analysis</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Analyze competitors and market position"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="technicalApproach"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Technical Approach</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe the technical solution approach"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pricingStrategy"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Pricing Strategy</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Detail the pricing approach and structure"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="teamComposition"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Team Composition</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Define the bid team structure and roles"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="resourceRequirements"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Resource Requirements</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="List required resources and equipment"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="deliverables"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Deliverables</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="List all project deliverables"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="milestones"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Key Milestones</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Define project milestones and timeline"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="qualityStandards"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Quality Standards</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Define quality standards and metrics"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="complianceRequirements"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Compliance Requirements</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="List all compliance and regulatory requirements"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex justify-end mt-6">
                {currentTab === "identification" ? (
                  <Button 
                    type="button" 
                    onClick={() => setCurrentTab("planning")}
                  >
                    Next 
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setCurrentTab("identification")}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back
                    </Button>
                    <Button 
                      type="submit" 
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Creating..." : "Create Bid"}
                      <Save className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}