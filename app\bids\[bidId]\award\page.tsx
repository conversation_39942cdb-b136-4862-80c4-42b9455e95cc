'use client';

import { use<PERSON>arams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Loader2, CheckCircle2, AlertTriangle, FileCheck, DollarSign, Users, Clock } from 'lucide-react';
import { useState } from 'react';

interface VendorScore {
  vendorId: string;
  companyName: string;
  scores: {
    technical: number;
    commercial: number;
    presentation: number;
  };
  totalScore: number;
  ranking: number;
  status: 'compliant' | 'non-compliant';
  dueDiligence: {
    completed: boolean;
    issues: string[];
  };
  pricing: {
    amount: number;
    currency: string;
    paymentTerms: string;
  };
  timeline: {
    startDate: string;
    duration: number;
    keyMilestones: number;
  };
}

// TODO: Replace with actual API calls when vendor scoring API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockVendorScores: VendorScore[] = [
  {
    vendorId: 'v1',
    companyName: 'Tech Solutions Inc.',
    scores: {
      technical: 8.5,
      commercial: 7.8,
      presentation: 9.0,
    },
    totalScore: 8.4,
    ranking: 1,
    status: 'compliant',
    dueDiligence: {
      completed: true,
      issues: [],
    },
    pricing: {
      amount: 150000,
      currency: 'USD',
      paymentTerms: 'Net 30',
    },
    timeline: {
      startDate: '2025-04-01',
      duration: 6,
      keyMilestones: 4,
    },
  },
  {
    vendorId: 'v2',
    companyName: 'Digital Innovations Ltd.',
    scores: {
      technical: 7.5,
      commercial: 8.2,
      presentation: 7.8,
    },
    totalScore: 7.8,
    ranking: 2,
    status: 'compliant',
    dueDiligence: {
      completed: true,
      issues: ['Pending certification renewal'],
    },
    pricing: {
      amount: 165000,
      currency: 'USD',
      paymentTerms: 'Net 45',
    },
    timeline: {
      startDate: '2025-04-15',
      duration: 7,
      keyMilestones: 5,
    },
  },
];

const recommendationSchema = z.object({
  selectedVendorId: z.string().min(1, 'Please select a vendor'),
  justification: z.string().min(100, 'Please provide a detailed justification'),
  riskMitigation: z.string().min(50, 'Please outline risk mitigation strategies'),
  nextSteps: z.string().min(50, 'Please outline the next steps'),
  contractTerms: z.string().min(50, 'Please specify key contract terms'),
});

type RecommendationForm = z.infer<typeof recommendationSchema>;

export default function AwardPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [vendorScores] = useState<VendorScore[]>(mockVendorScores);

  const form = useForm<RecommendationForm>({
    resolver: zodResolver(recommendationSchema),
    defaultValues: {
      selectedVendorId: '',
      justification: '',
      riskMitigation: '',
      nextSteps: '',
      contractTerms: '',
    },
  });

  const chartData = vendorScores.map(vendor => ({
    name: vendor.companyName,
    Technical: vendor.scores.technical,
    Commercial: vendor.scores.commercial,
    Presentation: vendor.scores.presentation,
  }));

  const onSubmit = async (data: RecommendationForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit award recommendation
      console.log('Submitting award recommendation:', data);

      toast({
        title: "Recommendation Submitted",
        description: "Your award recommendation has been submitted for approval.",
      });

      form.reset();
    } catch (error) {
      console.error('Error submitting recommendation:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your recommendation.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Award Recommendation</CardTitle>
            <CardDescription>
              Review vendor evaluations and submit your award recommendation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="comparison">Comparison</TabsTrigger>
                <TabsTrigger value="recommendation">Recommendation</TabsTrigger>
              </TabsList>

              <TabsContent value="summary">
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {vendorScores.map((vendor) => (
                      <Card key={vendor.vendorId}>
                        <CardHeader>
                          <CardTitle className="text-lg">{vendor.companyName}</CardTitle>
                          <div className="flex items-center space-x-2">
                            <Badge variant={vendor.status === 'compliant' ? 'default' : 'destructive'}>
                              {vendor.status}
                            </Badge>
                            <Badge variant="secondary">
                              Rank #{vendor.ranking}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div>
                              <div className="text-sm font-medium">Overall Score</div>
                              <div className="text-2xl font-bold">{vendor.totalScore.toFixed(1)}</div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span>Technical</span>
                                <span>{vendor.scores.technical.toFixed(1)}</span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span>Commercial</span>
                                <span>{vendor.scores.commercial.toFixed(1)}</span>
                              </div>
                              <div className="flex items-center justify-between text-sm">
                                <span>Presentation</span>
                                <span>{vendor.scores.presentation.toFixed(1)}</span>
                              </div>
                            </div>
                            <div className="pt-4 border-t">
                              <div className="grid grid-cols-2 gap-4">
                                <div className="flex items-center space-x-2 text-sm">
                                  <DollarSign className="h-4 w-4" />
                                  <span>{formatCurrency(vendor.pricing.amount)}</span>
                                </div>
                                <div className="flex items-center space-x-2 text-sm">
                                  <Clock className="h-4 w-4" />
                                  <span>{vendor.timeline.duration} months</span>
                                </div>
                              </div>
                            </div>
                            {vendor.dueDiligence.issues.length > 0 && (
                              <div className="pt-4 border-t">
                                <div className="flex items-center space-x-2 text-yellow-600">
                                  <AlertTriangle className="h-4 w-4" />
                                  <span className="text-sm font-medium">Due Diligence Issues</span>
                                </div>
                                <ul className="mt-2 text-sm space-y-1">
                                  {vendor.dueDiligence.issues.map((issue, index) => (
                                    <li key={index} className="text-gray-600">• {issue}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="comparison">
                <div className="space-y-8">
                  <Card>
                    <CardHeader>
                      <CardTitle>Score Comparison</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[400px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart data={chartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis domain={[0, 10]} />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="Technical" fill="#2563eb" />
                            <Bar dataKey="Commercial" fill="#16a34a" />
                            <Bar dataKey="Presentation" fill="#9333ea" />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Detailed Comparison</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Vendor</TableHead>
                            <TableHead>Total Score</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Timeline</TableHead>
                            <TableHead>Due Diligence</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {vendorScores.map((vendor) => (
                            <TableRow key={vendor.vendorId}>
                              <TableCell className="font-medium">
                                {vendor.companyName}
                              </TableCell>
                              <TableCell>{vendor.totalScore.toFixed(1)}</TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div>{formatCurrency(vendor.pricing.amount)}</div>
                                  <div className="text-sm text-gray-500">
                                    {vendor.pricing.paymentTerms}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div>{vendor.timeline.duration} months</div>
                                  <div className="text-sm text-gray-500">
                                    {vendor.timeline.keyMilestones} milestones
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {vendor.dueDiligence.completed ? (
                                  <div className="flex items-center space-x-2">
                                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                                    <span>Complete</span>
                                  </div>
                                ) : (
                                  <div className="flex items-center space-x-2">
                                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                    <span>Pending</span>
                                  </div>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="recommendation">
                <Card>
                  <CardHeader>
                    <CardTitle>Submit Recommendation</CardTitle>
                    <CardDescription>
                      Provide your award recommendation with detailed justification.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="selectedVendorId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Recommended Vendor</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a vendor" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {vendorScores.map((vendor) => (
                                    <SelectItem
                                      key={vendor.vendorId}
                                      value={vendor.vendorId}
                                    >
                                      {vendor.companyName}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="justification"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Justification</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Provide a detailed justification for your recommendation..."
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="riskMitigation"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Risk Mitigation</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Outline strategies to mitigate identified risks..."
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="nextSteps"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Next Steps</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Outline the next steps in the award process..."
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="contractTerms"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Key Contract Terms</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Specify the key terms to be included in the contract..."
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Submitting...
                            </>
                          ) : (
                            'Submit Recommendation'
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
