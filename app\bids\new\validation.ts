import * as z from "zod";

export const evaluationCriteriaSchema = z.object({
  technicalWeight: z.number()
    .min(0)
    .max(100)
    .default(40),
  commercialWeight: z.number()
    .min(0)
    .max(100)
    .default(40),
  presentationWeight: z.number()
    .min(0)
    .max(100)
    .default(20),
  minimumTechnicalScore: z.number()
    .min(0)
    .max(100)
    .optional(),
  minimumCommercialScore: z.number()
    .min(0)
    .max(100)
    .optional(),
});

export const bidFormSchema = z.object({
  // Opportunity Identification
  title: z.string()
    .min(2, "Title must be at least 2 characters")
    .max(100, "Title cannot exceed 100 characters"),
  description: z.string()
    .min(10, "Description must be at least 10 characters")
    .max(2000, "Description cannot exceed 2000 characters"),
  budget: z.number()
    .min(0, "Budget must be a positive number")
    .max(1000000000, "Budget cannot exceed 1 billion"),
  dueDate: z.date()
    .min(new Date(), "Due date must be in the future"),
  opportunitySource: z.enum(['rfp', 'direct', 'market', 'internal', 'partner'], {
    required_error: "Please select an opportunity source"
  }),
  evaluationCriteria: evaluationCriteriaSchema,
  
  // ... rest of the validation schema
}).refine((data) => {
  // Custom validation logic
  const today = new Date();
  const dueDate = new Date(data.dueDate);
  const minDays = 7;
  
  const diffTime = Math.abs(dueDate.getTime() - today.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays >= minDays;
}, {
  message: "Due date must be at least 7 days from today",
  path: ["dueDate"]
});