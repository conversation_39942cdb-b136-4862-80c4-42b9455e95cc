# SQLite Schema Documentation

## Entity Relationship Diagram

```
documents
┌─────────────────────┐
│ id (PK)             │
│ bidId               │◄───────────┐
│ title               │             │
│ content             │             │
│ createdAt           │             │
│ updatedAt           │             │
│ createdBy           │             │
│ status              │             │
│ lastVersionId       │             │
│ lastVersion         │             │
│ metadata            │             │
└─────────────────────┘             │
          ▲                         │
          │                         │
          │ 1:N                     │
          │                         │
document_versions                   │
┌─────────────────────┐             │
│ id (PK)             │             │
│ documentId (FK)     │             │
│ content             │             │
│ version             │             │
│ previousVersionId   │             │
│ diff                │             │
│ createdAt           │             │
│ createdBy_*         │             │
│ commitMessage       │             │
│ status              │             │
└─────────────────────┘             │
          ▲                         │
          │                         │
          │ 1:N                     │
          │                         │
document_comments                   │
┌─────────────────────┐             │
│ id (PK)             │             │
│ documentId (FK)     │             │
│ versionId (FK)      │             │
│ content             │             │
│ author_*            │             │
│ createdAt           │             │
│ selection_*         │             │
│ resolved            │             │
│ parent_comment_id   │◄─┐          │
└─────────────────────┘  │          │
          ▲              │          │
          └──────────────┘          │
          (Self-reference)          │
                                    │
document_edits                      │
┌─────────────────────┐             │
│ id (PK)             │             │
│ documentId (FK)     │             │
│ content             │             │
│ timestamp           │             │
└─────────────────────┘             │
          ▲                         │
          └─────────────────────────┘
```

## Relationship Mapping

1. **Document to Document Versions (1:N)**
   - A document can have multiple versions
   - Each version belongs to exactly one document
   - Foreign key: `document_versions.documentId` references `documents.id`

2. **Document to Document Comments (1:N)**
   - A document can have multiple comments
   - Each comment belongs to exactly one document
   - Foreign key: `document_comments.documentId` references `documents.id`

3. **Document Version to Comments (1:N)**
   - A document version can have multiple comments
   - A comment can be associated with a specific version
   - Foreign key: `document_comments.versionId` references `document_versions.id`

4. **Comment to Comment (Self-Reference)**
   - Comments can have replies (parent-child relationship)
   - Foreign key: `document_comments.parent_comment_id` self-references `document_comments.id`

5. **Document to Document Edits (1:N)**
   - A document can have multiple edits (for offline tracking)
   - Each edit belongs to exactly one document
   - Foreign key: `document_edits.documentId` references `documents.id`

## Data Migration Mapping

### From IndexedDB to SQLite

1. **documents store → documents table**
   - Direct field mapping for most fields
   - Any additional properties in IndexedDB stored as JSON in `metadata` field

2. **documentVersions store → document_versions table**
   - `metadata.createdAt` → `createdAt`
   - `metadata.createdBy.id` → `createdBy_id`
   - `metadata.createdBy.name` → `createdBy_name`
   - `metadata.createdBy.role` → `createdBy_role`
   - `metadata.commitMessage` → `commitMessage`
   - `metadata.status` → `status`

3. **documentComments store → document_comments table**
   - `author.id` → `author_id`
   - `author.name` → `author_name`
   - `author.avatar` → `author_avatar`
   - `selection.from` → `selection_from`
   - `selection.to` → `selection_to`
   - `replies` array handled via parent_comment_id foreign key

4. **documentEdits store → document_edits table**
   - Direct field mapping 