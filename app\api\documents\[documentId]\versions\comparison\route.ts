import { NextResponse } from "next/server";
import { DocumentVersionControl, getDocumentVersion } from "@/lib/documents";

const versionControl = new DocumentVersionControl();

/**
 * Compare two document versions and return the differences between them
 */
export async function GET(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  try {
    const searchParams = new URL(req.url).searchParams;
    const sourceVersionId = searchParams.get("sourceVersionId");
    const targetVersionId = searchParams.get("targetVersionId");
    
    if (!sourceVersionId || !targetVersionId) {
      return NextResponse.json(
        { error: "Both sourceVersionId and targetVersionId are required" },
        { status: 400 }
      );
    }
    
    // Get the two versions to compare
    const sourceVersion = await getDocumentVersion(sourceVersionId);
    const targetVersion = await getDocumentVersion(targetVersionId);
    
    if (!sourceVersion || !targetVersion) {
      return NextResponse.json(
        { error: "One or both versions not found" },
        { status: 404 }
      );
    }
    
    // Get differences between the two versions
    const differences = await versionControl.compareVersions(sourceVersion, targetVersion);
    
    return NextResponse.json({
      sourceVersion: {
        id: sourceVersion.id,
        version: sourceVersion.version,
        metadata: sourceVersion.metadata,
      },
      targetVersion: {
        id: targetVersion.id,
        version: targetVersion.version,
        metadata: targetVersion.metadata,
      },
      differences
    });
  } catch (error) {
    console.error("Error comparing versions:", error);
    return NextResponse.json(
      { error: "Failed to compare versions" },
      { status: 500 }
    );
  }
}
