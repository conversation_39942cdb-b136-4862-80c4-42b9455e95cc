'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { TaskItem } from '@/types/task';
import { getMyTasks } from '@/services/taskService';

export function TaskList() {
  const [tasks, setTasks] = useState<TaskItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('TaskList component mounted, loading tasks...');
    
    const loadTasks = async () => {
      try {
        setIsLoading(true);
        setError(null);
        console.log('Calling getMyTasks()...');
        
        const data = await getMyTasks();
        console.log('Received tasks data:', {
          taskCount: data.length,
          sampleTask: data[0] || null
        });
        
        setTasks(data);
      } catch (err) {
        console.error('Failed to load tasks:', err);
        setError(`Failed to load tasks: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setTasks([]);
      } finally {
        console.log('Task loading completed');
        setIsLoading(false);
      }
    };

    loadTasks();
    
    // Cleanup function
    return () => {
      console.log('TaskList component unmounting');
    };
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-20 w-full" />
        ))}
      </div>
    );
  }


  if (error) {
    return (
      <Card className="bg-destructive/10 border-destructive">
        <CardContent className="pt-6">
          <p className="text-destructive">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6 text-center text-muted-foreground">
          <p>No tasks found.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <Card key={task._links.item.href} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className="text-lg">
                {task.Task.Subject}
              </CardTitle>
              <span className="text-sm text-muted-foreground">
                {new Date(task.ParentEntity$Tracking.CreatedDate).toLocaleDateString()}
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Bid</p>
                <p className="font-medium">{task.ParentEntity$Properties.Bid_Title}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Status</p>
                <p className="font-medium">{task.ParentEntity$Lifecycle.CurrentState}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Budget</p>
                <p className="font-medium">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 0
                  }).format(Number(task.ParentEntity$Properties.Bid_Budget) || 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
