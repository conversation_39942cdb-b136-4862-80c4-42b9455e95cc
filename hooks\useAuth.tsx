'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { OTDSAuthResponse, validateAppWorksSession } from '@/lib/auth';
import { clearSAMLArtifact } from '@/lib/samlUtils';

interface AuthContextType {
  user: OTDSAuthResponse | null;
  login: (user: OTDSAuthResponse) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * AuthProvider is a context provider component that manages and provides authentication state
 * and methods to its children components. It handles user authentication, session initialization,
 * login, and logout processes, while also managing loading and error states.
 *
 * @param {Object} props - The properties of the component.
 * @param {React.ReactNode} props.children - The child components that consume the authentication context.
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<OTDSAuthResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const response = await fetch('/api/auth/session');
        if (response.ok) {
          const data = await response.json();
          if (data.user) {
            setUser(data.user);
            // Validate session and store SAMLart
            await validateAppWorksSession(data.user.ticket);
          }
        }
      } catch (err) {
        console.error('Failed to restore auth state:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  /**
   * Authenticates a user by validating the user's session and storing the SAMLart.
   * If the session is invalid, it throws an error.
   * If the authentication is successful, it sets the user state and clears any error.
   * @param {OTDSAuthResponse} userData - The user data returned from the authentication endpoint.
   * @throws {Error} If the session is invalid or the authentication fails.
   */
  const login = async (userData: OTDSAuthResponse) => {
    try {
      // First validate the session to get and store the SAMLart
      const isValid = await validateAppWorksSession(userData.ticket);
      if (!isValid) {
        throw new Error('Failed to validate session');
      }
      
      setUser(userData);
      setError(null);
    } catch (err) {
      console.error('Login failed:', err);
      setError('Authentication failed. Please try again.');
      throw err;
    }
  };

  const logout = async () => {
    try {
      await fetch('/api/auth', { 
        method: 'DELETE',
        credentials: 'include'
      });
      setUser(null);
      setError(null);
      clearSAMLArtifact(); // Clear SAML artifact on logout
    } catch (err) {
      console.error('Logout failed:', err);
      setError('Failed to logout. Please try again.');
    }
  };

  const clearError = () => setError(null);

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading, error, clearError }}>
      {children}
    </AuthContext.Provider>
  );
}

/**
 * useAuth is a hook that allows components to access the authentication state and
 * methods without having to pass props down manually.
 *
 * @returns {AuthContextType} The authentication state and methods.
 * @throws {Error} If the hook is called outside of an AuthProvider.
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
