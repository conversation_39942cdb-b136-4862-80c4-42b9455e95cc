"use client";

import * as React from "react";
import { <PERSON>, Sun, Lapt<PERSON> } from "lucide-react";
import { useTheme } from "@/components/theme-provider";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();

  const handleKeyDown = (event: React.KeyboardEvent, newTheme: "light" | "dark" | "system") => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      setTheme(newTheme);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          aria-label="Toggle theme"
          tabIndex={0}
          className="rounded-full w-9 h-9 border-primary/20 hover:bg-accent/10 hover:text-accent transition-all duration-300"
        >
          <Sun className="h-5 w-5 rotate-0 scale-100 transition-all duration-300 dark:-rotate-90 dark:scale-0 text-amber-500" />
          <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all duration-300 dark:rotate-0 dark:scale-100 text-indigo-400" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="rounded-xl border-primary/20 shadow-lg">
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          onKeyDown={(e) => handleKeyDown(e, "light")}
          tabIndex={0}
          className="flex items-center gap-2 cursor-pointer hover:bg-accent/10 rounded-lg my-1 transition-colors duration-200"
          aria-label="Light theme"
        >
          <Sun className="h-4 w-4 text-amber-500" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          onKeyDown={(e) => handleKeyDown(e, "dark")}
          tabIndex={0}
          className="flex items-center gap-2 cursor-pointer hover:bg-accent/10 rounded-lg my-1 transition-colors duration-200"
          aria-label="Dark theme"
        >
          <Moon className="h-4 w-4 text-indigo-400" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          onKeyDown={(e) => handleKeyDown(e, "system")}
          tabIndex={0}
          className="flex items-center gap-2 cursor-pointer hover:bg-accent/10 rounded-lg my-1 transition-colors duration-200"
          aria-label="System theme"
        >
          <Laptop className="h-4 w-4 text-gray-500" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
