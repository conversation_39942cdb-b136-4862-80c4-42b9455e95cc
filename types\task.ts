export interface TaskListResponse {
  page: {
    skip: number;
    top: number;
    count: number;
  };
  _links: {
    self: {
      href: string;
    };
    first: {
      href: string;
    };
  };
  _embedded: {
    BidDefaultTaskList: TaskItem[];
  };
}

export interface TaskItem {
  _links: {
    item: {
      href: string;
    };
  };
  Task: {
    TaskOwner: string;
    TaskOwnerName: string;
    Subject: string;
    State: string;
    SourceName: string;
    ParentTaskId: string;
  };
  ParentEntity$Tracking: {
    CreatedDate: string;
  };
  ParentEntity$Lifecycle: {
    CurrentState: string;
  };
  ParentEntity$Properties: {
    Bid_Title: string;
    Bid_Status: string;
    Bid_Budget: string;
  };
  ParentEntity$Identity: {
    Id: string;
  };
}

export interface TaskListRequest {
  distinct?: number;
  skip?: number;
  top?: number;
  parameters?: {
    [key: string]: {
      name: string;
      comparison: {
        value?: string | number | boolean;
        values?: (string | number | boolean)[];
        from?: string | number;
        to?: string | number;
        operator: 'eq' | 'ne' | 'lt' | 'gt' | 'ge' | 'le' | 'Like' | 'IsNull' | 'IsNotNull' | 'InList' | 'NotInList' | 'Between';
      };
    };
  };
  orderBy?: Array<{
    name: string;
    sortType?: 'ASC' | 'DESC';
  }>;
  select?: Array<{
    name: string;
  }>;
}
