'use client';

import { useParams } from 'next/navigation';
import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Loader2,
  FileText,
  History,
  Download,
  Eye,
  PenLine,
  CheckCircle2,
  Clock,
  AlertTriangle,
} from 'lucide-react';

interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  lastUpdated: string;
  fields: {
    id: string;
    name: string;
    type: 'text' | 'date' | 'number' | 'select';
    required: boolean;
    options?: string[];
  }[];
}

interface ContractVersion {
  id: string;
  version: string;
  status: 'draft' | 'review' | 'final';
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    role: string;
  };
  comments?: string;
}

// TODO: Replace with actual API calls when contract template API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockTemplates: ContractTemplate[] = [
  {
    id: 't1',
    name: 'Standard Service Agreement',
    description: 'Standard template for service-based contracts',
    version: '2.1',
    lastUpdated: '2025-03-01T10:00:00Z',
    fields: [
      {
        id: 'f1',
        name: 'startDate',
        type: 'date',
        required: true,
      },
      {
        id: 'f2',
        name: 'contractValue',
        type: 'number',
        required: true,
      },
      {
        id: 'f3',
        name: 'paymentTerms',
        type: 'select',
        required: true,
        options: ['Net 30', 'Net 45', 'Net 60'],
      },
      {
        id: 'f4',
        name: 'serviceDescription',
        type: 'text',
        required: true,
      },
    ],
  },
  {
    id: 't2',
    name: 'Professional Services Agreement',
    description: 'Template for professional consulting services',
    version: '1.5',
    lastUpdated: '2025-02-15T10:00:00Z',
    fields: [
      {
        id: 'f5',
        name: 'consultantName',
        type: 'text',
        required: true,
      },
      {
        id: 'f6',
        name: 'ratePerHour',
        type: 'number',
        required: true,
      },
    ],
  },
];

const mockVersions: ContractVersion[] = [
  {
    id: 'v1',
    version: '1.0',
    status: 'final',
    createdAt: '2025-03-15T10:00:00Z',
    createdBy: {
      id: 'u1',
      name: 'John Smith',
      role: 'Contract Manager',
    },
    comments: 'Initial contract draft',
  },
  {
    id: 'v2',
    version: '1.1',
    status: 'review',
    createdAt: '2025-03-16T10:00:00Z',
    createdBy: {
      id: 'u2',
      name: 'Sarah Johnson',
      role: 'Legal Counsel',
    },
    comments: 'Updated payment terms and SLA requirements',
  },
];

const contractSchema = z.object({
  templateId: z.string().min(1, 'Please select a template'),
  fields: z.record(z.string()),
  comments: z.string().min(10, 'Please provide comments about this version'),
});

type ContractForm = z.infer<typeof contractSchema>;

export default function ContractPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ContractTemplate | null>(null);
  const [templates] = useState<ContractTemplate[]>(mockTemplates);
  const [versions] = useState<ContractVersion[]>(mockVersions);

  const form = useForm<ContractForm>({
    resolver: zodResolver(contractSchema),
    defaultValues: {
      templateId: '',
      fields: {},
      comments: '',
    },
  });

  const onSubmit = async (data: ContractForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to generate contract
      console.log('Generating contract:', data);

      toast({
        title: "Contract Generated",
        description: "New contract version has been created and sent for review.",
      });

      form.reset();
    } catch (error) {
      console.error('Error generating contract:', error);
      toast({
        title: "Generation Failed",
        description: "There was an error generating the contract.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
    form.setValue('templateId', templateId);

    // Initialize fields with empty values
    if (template) {
      const fields: Record<string, string> = {};
      template.fields.forEach(field => {
        fields[field.id] = '';
      });
      form.setValue('fields', fields);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'final':
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Final
          </Badge>
        );
      case 'review':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            In Review
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <PenLine className="h-3 w-3 mr-1" />
            Draft
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Contract Generation</CardTitle>
            <CardDescription>
              Generate and manage contract versions for bid {bidId}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="generate">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="generate">Generate Contract</TabsTrigger>
                <TabsTrigger value="versions">Version History</TabsTrigger>
              </TabsList>

              <TabsContent value="generate">
                <Card>
                  <CardHeader>
                    <CardTitle>New Contract Version</CardTitle>
                    <CardDescription>
                      Select a template and fill in the required fields
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                          control={form.control}
                          name="templateId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Contract Template</FormLabel>
                              <Select
                                onValueChange={(value) => handleTemplateChange(value)}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a template" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {templates.map((template) => (
                                    <SelectItem
                                      key={template.id}
                                      value={template.id}
                                    >
                                      <div>
                                        {template.name}
                                        <div className="text-xs text-gray-500">
                                          Version {template.version}
                                        </div>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {selectedTemplate && (
                          <div className="space-y-4">
                            {selectedTemplate.fields.map((field) => (
                              <FormField
                                key={field.id}
                                control={form.control}
                                name={`fields.${field.id}`}
                                render={({ field: formField }) => (
                                  <FormItem>
                                    <FormLabel>{field.name}</FormLabel>
                                    <FormControl>
                                      {field.type === 'select' ? (
                                        <Select
                                          onValueChange={formField.onChange}
                                          defaultValue={formField.value}
                                        >
                                          <FormControl>
                                            <SelectTrigger>
                                              <SelectValue placeholder={`Select ${field.name}`} />
                                            </SelectTrigger>
                                          </FormControl>
                                          <SelectContent>
                                            {field.options?.map((option) => (
                                              <SelectItem
                                                key={option}
                                                value={option}
                                              >
                                                {option}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      ) : field.type === 'text' ? (
                                        <Textarea
                                          placeholder={`Enter ${field.name}`}
                                          {...formField}
                                        />
                                      ) : (
                                        <Input
                                          type={field.type}
                                          placeholder={`Enter ${field.name}`}
                                          {...formField}
                                        />
                                      )}
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            ))}

                            <FormField
                              control={form.control}
                              name="comments"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Version Comments</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Add comments about this version..."
                                      className="min-h-[100px]"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        )}

                        <Button
                          type="submit"
                          className="w-full"
                          disabled={isSubmitting || !selectedTemplate}
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            <>
                              <FileText className="mr-2 h-4 w-4" />
                              Generate Contract
                            </>
                          )}
                        </Button>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="versions">
                <Card>
                  <CardHeader>
                    <CardTitle>Version History</CardTitle>
                    <CardDescription>
                      Track and manage contract versions
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Version</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created By</TableHead>
                          <TableHead>Created At</TableHead>
                          <TableHead>Comments</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {versions.map((version) => (
                          <TableRow key={version.id}>
                            <TableCell className="font-medium">
                              v{version.version}
                            </TableCell>
                            <TableCell>{getStatusBadge(version.status)}</TableCell>
                            <TableCell>
                              <div>
                                {version.createdBy.name}
                                <div className="text-sm text-gray-500">
                                  {version.createdBy.role}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>{formatDate(version.createdAt)}</TableCell>
                            <TableCell>{version.comments}</TableCell>
                            <TableCell>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => {
                                    // TODO: Implement view action
                                    console.log('View version:', version.id);
                                  }}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() => {
                                    // TODO: Implement download action
                                    console.log('Download version:', version.id);
                                  }}
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
