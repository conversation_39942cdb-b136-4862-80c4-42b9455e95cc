'use client';

import { useParams } from 'next/navigation';
import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
} from 'recharts';
import {
  Loader2,
  CheckCircle2,
  AlertTriangle,
  Clock,
  Calendar,
  Target,
  TrendingUp,
  FileCheck,
  Plus,
} from 'lucide-react';
import { DependencyGraph } from '@/components/tracking/dependency-graph';

interface Milestone {
  id: string;
  name: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'completed' | 'delayed' | 'at_risk';
  progress: number;
  dependencies?: string[];
  assignedTo?: {
    id: string;
    name: string;
    role: string;
  };
  comments?: {
    id: string;
    text: string;
    createdAt: string;
    createdBy: {
      id: string;
      name: string;
      role: string;
    };
  }[];
}

interface ProgressReport {
  id: string;
  date: string;
  metrics: {
    completedMilestones: number;
    totalMilestones: number;
    onTrackPercentage: number;
    atRiskPercentage: number;
    delayedPercentage: number;
  };
  keyUpdates: string[];
  challenges: string[];
  nextSteps: string[];
}

// TODO: Replace with actual API calls when milestone tracking API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockMilestones: Milestone[] = [
  {
    id: 'm1',
    name: 'Contract Signing',
    description: 'Complete contract signing with all parties',
    dueDate: '2025-04-01T10:00:00Z',
    status: 'completed',
    progress: 100,
    assignedTo: {
      id: 'u1',
      name: 'John Smith',
      role: 'Contract Manager',
    },
    comments: [
      {
        id: 'c1',
        text: 'All parties have signed the contract',
        createdAt: '2025-03-20T15:00:00Z',
        createdBy: {
          id: 'u1',
          name: 'John Smith',
          role: 'Contract Manager',
        },
      },
    ],
  },
  {
    id: 'm2',
    name: 'Project Kickoff',
    description: 'Initial project kickoff meeting with vendor',
    dueDate: '2025-04-15T10:00:00Z',
    status: 'pending',
    progress: 50,
    dependencies: ['m1'],
    assignedTo: {
      id: 'u2',
      name: 'Sarah Johnson',
      role: 'Project Manager',
    },
  },
  {
    id: 'm3',
    name: 'Requirements Review',
    description: 'Detailed review of project requirements',
    dueDate: '2025-05-01T10:00:00Z',
    status: 'at_risk',
    progress: 20,
    dependencies: ['m2'],
    assignedTo: {
      id: 'u3',
      name: 'Michael Chen',
      role: 'Technical Lead',
    },
    comments: [
      {
        id: 'c2',
        text: 'Some requirements need clarification',
        createdAt: '2025-04-20T11:00:00Z',
        createdBy: {
          id: 'u3',
          name: 'Michael Chen',
          role: 'Technical Lead',
        },
      },
    ],
  },
];

const mockProgressReports: ProgressReport[] = [
  {
    id: 'r1',
    date: '2025-03-15',
    metrics: {
      completedMilestones: 1,
      totalMilestones: 3,
      onTrackPercentage: 33,
      atRiskPercentage: 33,
      delayedPercentage: 0,
    },
    keyUpdates: [
      'Contract signed by all parties',
      'Project kickoff scheduled',
    ],
    challenges: [
      'Requirements clarification needed',
    ],
    nextSteps: [
      'Complete project kickoff',
      'Schedule requirements review',
    ],
  },
  {
    id: 'r2',
    date: '2025-03-22',
    metrics: {
      completedMilestones: 1,
      totalMilestones: 3,
      onTrackPercentage: 66,
      atRiskPercentage: 33,
      delayedPercentage: 0,
    },
    keyUpdates: [
      'Project kickoff preparations underway',
      'Initial requirements document reviewed',
    ],
    challenges: [
      'Resource availability for requirements review',
    ],
    nextSteps: [
      'Finalize kickoff presentation',
      'Schedule technical team meetings',
    ],
  },
];

const updateSchema = z.object({
  milestoneId: z.string().min(1, 'Please select a milestone'),
  status: z.enum(['pending', 'completed', 'delayed', 'at_risk']),
  progress: z.number().min(0).max(100),
  comment: z.string().min(10, 'Please provide a detailed comment'),
});

type UpdateForm = z.infer<typeof updateSchema>;

export default function TrackingPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [milestones] = useState<Milestone[]>(mockMilestones);
  const [progressReports] = useState<ProgressReport[]>(mockProgressReports);

  // Transform milestones data for dependency graph
  const dependencyNodes = milestones.map(milestone => ({
    id: milestone.id,
    name: milestone.name,
    status: milestone.status,
    progress: milestone.progress,
    dueDate: milestone.dueDate,
  }));

  const dependencyLinks = milestones
    .filter(milestone => milestone.dependencies)
    .flatMap(milestone =>
      (milestone.dependencies || []).map(dependencyId => ({
        source: dependencyId,
        target: milestone.id,
      }))
    );

  const form = useForm<UpdateForm>({
    resolver: zodResolver(updateSchema),
    defaultValues: {
      milestoneId: '',
      status: 'pending',
      progress: 0,
      comment: '',
    },
  });

  const onSubmit = async (data: UpdateForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to update milestone
      console.log('Updating milestone:', data);

      toast({
        title: "Update Submitted",
        description: "The milestone has been updated successfully.",
      });

      form.reset();
    } catch (error) {
      console.error('Error updating milestone:', error);
      toast({
        title: "Update Failed",
        description: "There was an error updating the milestone.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case 'delayed':
        return (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Delayed
          </Badge>
        );
      case 'at_risk':
        return (
          <Badge variant="secondary" className="bg-yellow-500">
            <AlertTriangle className="h-3 w-3 mr-1" />
            At Risk
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const progressTrendData = progressReports.map(report => ({
    date: report.date,
    'On Track': report.metrics.onTrackPercentage,
    'At Risk': report.metrics.atRiskPercentage,
    'Delayed': report.metrics.delayedPercentage,
  }));

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Post-Submission Tracking</CardTitle>
            <CardDescription>
              Track milestones and progress for bid {bidId}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="dashboard">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
                <TabsTrigger value="milestones">Milestones</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
              </TabsList>

              <TabsContent value="dashboard">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Total Milestones
                      </CardTitle>
                      <Target className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {milestones.length}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Completed
                      </CardTitle>
                      <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {milestones.filter(m => m.status === 'completed').length}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        At Risk
                      </CardTitle>
                      <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {milestones.filter(m => m.status === 'at_risk').length}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Overall Progress
                      </CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {Math.round(
                          milestones.reduce((acc, m) => acc + m.progress, 0) /
                            milestones.length
                        )}%
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <Card>
                    <CardHeader>
                      <CardTitle>Progress Trend</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[300px]">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={progressTrendData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="On Track"
                              stroke="#16a34a"
                              strokeWidth={2}
                            />
                            <Line
                              type="monotone"
                              dataKey="At Risk"
                              stroke="#eab308"
                              strokeWidth={2}
                            />
                            <Line
                              type="monotone"
                              dataKey="Delayed"
                              stroke="#dc2626"
                              strokeWidth={2}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Updates</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-[300px]">
                        <div className="space-y-4">
                          {milestones
                            .flatMap(m => m.comments || [])
                            .sort((a, b) =>
                              new Date(b.createdAt).getTime() -
                              new Date(a.createdAt).getTime()
                            )
                            .map(comment => (
                              <div
                                key={comment.id}
                                className="flex items-start space-x-4 p-4 rounded-lg border"
                              >
                                <div className="flex-1">
                                  <div className="text-sm font-medium">
                                    {comment.createdBy.name}
                                    <span className="text-gray-500 ml-2">
                                      {comment.createdBy.role}
                                    </span>
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {formatDate(comment.createdAt)}
                                  </div>
                                  <div className="mt-1">
                                    {comment.text}
                                  </div>
                                </div>
                              </div>
                            ))}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="milestones">
                <div className="space-y-8">
                  <DependencyGraph
                    nodes={dependencyNodes}
                    links={dependencyLinks}
                  />

                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle>Milestone List</CardTitle>
                        <Button
                          onClick={() => {
                            // TODO: Implement add milestone
                            console.log('Add milestone');
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Milestone
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Milestone</TableHead>
                            <TableHead>Due Date</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Progress</TableHead>
                            <TableHead>Assigned To</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {milestones.map((milestone) => (
                            <TableRow key={milestone.id}>
                              <TableCell>
                                <div>
                                  <div className="font-medium">
                                    {milestone.name}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {milestone.description}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                {formatDate(milestone.dueDate)}
                              </TableCell>
                              <TableCell>
                                {getStatusBadge(milestone.status)}
                              </TableCell>
                              <TableCell>
                                <div className="w-full bg-gray-200 rounded-full h-2.5">
                                  <div
                                    className={`h-2.5 rounded-full ${getProgressColor(
                                      milestone.progress
                                    )}`}
                                    style={{ width: `${milestone.progress}%` }}
                                  ></div>
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                  {milestone.progress}%
                                </div>
                              </TableCell>
                              <TableCell>
                                {milestone.assignedTo ? (
                                  <div>
                                    {milestone.assignedTo.name}
                                    <div className="text-sm text-gray-500">
                                      {milestone.assignedTo.role}
                                    </div>
                                  </div>
                                ) : (
                                  '-'
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Update Milestone</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                          <FormField
                            control={form.control}
                            name="milestoneId"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Milestone</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select a milestone" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {milestones.map((milestone) => (
                                      <SelectItem
                                        key={milestone.id}
                                        value={milestone.id}
                                      >
                                        {milestone.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Status</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  defaultValue={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="delayed">Delayed</SelectItem>
                                    <SelectItem value="at_risk">At Risk</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="progress"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Progress (%)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    max="100"
                                    {...field}
                                    onChange={e =>
                                      field.onChange(parseInt(e.target.value))
                                    }
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="comment"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Update Comment</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Provide details about this update..."
                                    className="min-h-[100px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="submit"
                            className="w-full"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Updating...
                              </>
                            ) : (
                              <>
                                <FileCheck className="mr-2 h-4 w-4" />
                                Update Milestone
                              </>
                            )}
                          </Button>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="reports">
                <div className="space-y-8">
                  {progressReports.map((report) => (
                    <Card key={report.id}>
                      <CardHeader>
                        <CardTitle>Progress Report - {report.date}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <div className="text-sm font-medium text-gray-500">
                                Milestones
                              </div>
                              <div className="mt-1">
                                {report.metrics.completedMilestones} of{' '}
                                {report.metrics.totalMilestones} completed
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-500">
                                On Track
                              </div>
                              <div className="mt-1">
                                {report.metrics.onTrackPercentage}%
                              </div>
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-500">
                                At Risk/Delayed
                              </div>
                              <div className="mt-1">
                                {report.metrics.atRiskPercentage +
                                  report.metrics.delayedPercentage}%
                              </div>
                            </div>
                          </div>

                          <div>
                            <div className="text-sm font-medium mb-2">
                              Key Updates
                            </div>
                            <ul className="list-disc pl-5 space-y-1">
                              {report.keyUpdates.map((update, index) => (
                                <li key={index}>{update}</li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <div className="text-sm font-medium mb-2">
                              Challenges
                            </div>
                            <ul className="list-disc pl-5 space-y-1">
                              {report.challenges.map((challenge, index) => (
                                <li key={index}>{challenge}</li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <div className="text-sm font-medium mb-2">
                              Next Steps
                            </div>
                            <ul className="list-disc pl-5 space-y-1">
                              {report.nextSteps.map((step, index) => (
                                <li key={index}>{step}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
