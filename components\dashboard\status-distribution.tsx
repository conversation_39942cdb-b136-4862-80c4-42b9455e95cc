'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

interface StatusDistributionProps {
  data: Record<string, number>;
}

export function StatusDistribution({ data }: StatusDistributionProps) {
  // Transform the data for the pie chart
  const chartData = Object.entries(data).map(([name, value]) => ({
    name,
    value
  }));

  // Define colors for different statuses
  const COLORS = {
    'DRAFT': '#94a3b8', // slate-400
    'PENDING': '#f59e0b', // amber-500
    'APPROVED': '#10b981', // emerald-500
    'REJECTED': '#ef4444', // red-500
    'CLOSED': '#6366f1', // indigo-500
    'UNKNOWN': '#cbd5e1', // slate-300
  };

  // Default color for any status not in the COLORS object
  const DEFAULT_COLOR = '#cbd5e1';

  if (!chartData.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Bid Status Distribution</CardTitle>
          <CardDescription>
            Distribution of bids by their current status
          </CardDescription>
        </CardHeader>
        <CardContent className="h-[300px] flex items-center justify-center">
          <p className="text-muted-foreground">No data available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Bid Status Distribution</CardTitle>
        <CardDescription>
          Distribution of bids by their current status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={COLORS[entry.name as keyof typeof COLORS] || DEFAULT_COLOR} 
                  />
                ))}
              </Pie>
              <Tooltip formatter={(value) => [`${value} bids`, 'Count']} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
