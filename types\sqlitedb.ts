import { DocumentComment, DocumentVersion, DocumentStatus } from './index';

export interface DocumentEdit {
  id: string;
  documentId: string;
  content: string;
  timestamp: string;
}

export interface Document {
  id: string;
  bidId: string;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  status: DocumentStatus;
  lastVersionId?: string;
  lastVersion?: string;
  metadata?: Record<string, any>;
}

export interface SQLiteDatabase {
  // Database operations
  initSQLite: () => Promise<void>;
  closeSQLite: () => Promise<void>;
  isSQLiteAvailable: () => boolean;
  executeSQL: (sql: string, params?: any[]) => any;
  runSQL: (sql: string, params?: any[]) => void;
  execSQL: (sql: string) => any[];
  
  // Document operations
  saveDocument: (document: Document) => Promise<void>;
  getDocument: (id: string) => Promise<Document | null>;
  getDocumentsByBid: (bidId: string) => Promise<Document[]>;
  
  // Document version operations
  saveDocumentVersion: (version: DocumentVersion) => Promise<void>;
  getDocumentVersion: (id: string) => Promise<DocumentVersion | null>;
  getDocumentVersions: (documentId: string) => Promise<DocumentVersion[]>;
  getLatestVersion: (documentId: string) => Promise<DocumentVersion | null>;
  
  // Document comment operations
  saveDocumentComment: (comment: DocumentComment) => Promise<void>;
  getDocumentComments: (documentId: string, versionId?: string) => Promise<DocumentComment[]>;
  getDocumentCommentsWithReplies: (documentId: string, versionId?: string) => Promise<DocumentComment[]>;
  
  // Document edit operations
  saveDocumentEdit: (edit: DocumentEdit) => Promise<void>;
  getDocumentEdits: (documentId: string) => Promise<DocumentEdit[]>;
  clearDocumentEdits: (documentId: string) => Promise<void>;
  
  // Sync operations
  setupOfflineSync: () => void;
  exportDatabase: () => Uint8Array;
  importDatabase: (data: Uint8Array) => void;
  
  // Utility operations
  hasSQLiteData: () => Promise<boolean>;
} 