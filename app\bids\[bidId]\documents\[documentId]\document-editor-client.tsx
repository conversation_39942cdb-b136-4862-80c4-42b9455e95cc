"use client";

import { DocumentEditor } from "@/components/documents/document-editor";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { DocumentVersion } from "@/types";
import { useAuth } from "@/hooks/useAuth";
import { useIndexedDB } from "@/hooks/useIndexedDB";
import { useEffect, useState } from "react";
import { Loader2, ArrowLeft, Save, AlertCircle, History } from "lucide-react";
import Link from "next/link";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface DocumentEditorClientProps {
  bidId: string;
  documentId: string;
}

export default function DocumentEditorClient({
  bidId,
  documentId,
}: DocumentEditorClientProps) {
  const [documentTitle, setDocumentTitle] = useState("Loading document...");
  const [documentContent, setDocumentContent] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [justCreatedVersion, setJustCreatedVersion] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const { 
    isDBAvailable,
    isOnline,
    isLoading: isDbLoading,
    document: localDocument,
    versions,
    saveDocumentContent,
    createVersion
  } = useIndexedDB(documentId, bidId);

  // Log document data for debugging
  useEffect(() => {
    console.log("Document ID:", documentId);
    console.log("Bid ID:", bidId);
    console.log("IndexedDB document:", localDocument);
    console.log("IndexedDB loading:", isDbLoading);
  }, [documentId, bidId, localDocument, isDbLoading]);

  // Fetch document content on component mount
  useEffect(() => {
    const fetchDocumentContent = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        setError(null);

        // Check if IndexedDB is available
        if (!isDBAvailable) {
          setError("Your browser doesn't support offline document storage");
          setIsLoading(false);
          return;
        }
        
        if (documentId === "new") {
          // Creating a new document
          setDocumentTitle("New Document");
          setDocumentContent("");
          setIsLoading(false);
          return;
        }
        
        // If useIndexedDB is still loading, keep waiting
        if (isDbLoading) {
          return;
        }
        
        // If document has been loaded from IndexedDB, verify it belongs to the current bid
        if (localDocument) {
          if (localDocument.bidId === bidId) {
            setDocumentTitle(localDocument.title || "Untitled Document");
            setDocumentContent(localDocument.content || "");
            setIsLoading(false);
          } else {
            setError(`This document doesn't belong to the current bid (${bidId})`);
            setIsLoading(false);
          }
          return;
        }
        
        // No document found in IndexedDB and loading is complete
        if (!isDbLoading && !localDocument) {
          // Try to directly query IndexedDB as a fallback
          try {
            const openRequest = indexedDB.open("BidManagementDB", 1);
            
            openRequest.onsuccess = (event) => {
              const db = (event.target as IDBOpenDBRequest).result;
              
              if (!db.objectStoreNames.contains("documents")) {
                setError("Document database structure is not valid");
                setIsLoading(false);
                return;
              }
              
              const transaction = db.transaction("documents", "readonly");
              const store = transaction.objectStore("documents");
              const request = store.get(documentId);
              
              request.onsuccess = () => {
                const document = request.result;
                
                if (document) {
                  // Verify the document belongs to the current bid
                  if (document.bidId === bidId) {
                    setDocumentTitle(document.title || "Untitled Document");
                    setDocumentContent(document.content || "");
                    setIsLoading(false);
                  } else {
                    setError(`This document doesn't belong to bid ${bidId}`);
                    setIsLoading(false);
                  }
                } else {
                  setError("Document not found. It may have been deleted or not yet created.");
                  setIsLoading(false);
                }
              };
              
              request.onerror = () => {
                setError("Failed to retrieve document from database");
                setIsLoading(false);
              };
              
              transaction.oncomplete = () => {
                db.close();
              };
            };
            
            openRequest.onerror = () => {
              setError("Could not access the document database");
              setIsLoading(false);
            };
          } catch (error) {
            console.error("Error in direct IndexedDB query:", error);
            setError("Failed to load document data");
            setIsLoading(false);
          }
        }
      } catch (error) {
        console.error("Error loading document:", error);
        setError(error instanceof Error ? error.message : "Failed to load document");
        setIsLoading(false);
      }
    };
    
    fetchDocumentContent();
  }, [bidId, documentId, user, isDBAvailable, localDocument, isDbLoading]);
  
  const handleSaveContent = async (content: string) => {
    if (!user) return;
    
    try {
      if (!isDBAvailable) {
        throw new Error("IndexedDB is not available");
      }
      
      // Save content to IndexedDB
      await saveDocumentContent(documentId, content);
      
      toast({
        title: "Document saved",
        description: isOnline 
          ? "Your changes have been saved." 
          : "Your changes have been saved locally and will sync when you're back online.",
      });
      
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Error",
        description: "Failed to save document. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  const handleCreateVersion = async (version: DocumentVersion) => {
    if (!user) return;
    
    try {
      if (!isDBAvailable) {
        throw new Error("IndexedDB is not available");
      }
      
      // Create new version in IndexedDB
      await createVersion({
        documentId,
        content: version.content,
        commitMessage: version.metadata.commitMessage || "New version created",
        userId: user.userId || "unknown",
        userName: user.displayName || user.userId || "Anonymous",
        userRole: user.role || "User"
      });
      
      // Set the flag to indicate we just created a version
      setJustCreatedVersion(true);
      
      // Reset the flag after a short delay to allow save operations to occur in future
      setTimeout(() => {
        setJustCreatedVersion(false);
      }, 5000);
      
      toast({
        title: "Version created",
        description: "A new version has been created successfully.",
      });
      
    } catch (error) {
      console.error("Error creating version:", error);
      toast({
        title: "Error",
        description: "Failed to create version. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-120px)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading document...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/bids/${bidId}`}>Bid Details</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/bids/${bidId}/documents`}>Documents</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>Error</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        
        <Button asChild variant="outline">
          <Link href={`/bids/${bidId}/documents`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="p-4 h-full flex flex-col">
      <header className="mb-6">
        <Breadcrumb className="mb-4">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={`/bids/${bidId}`}>Bid Details</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href={`/bids/${bidId}/documents`}>Documents</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>{documentTitle}</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">{documentTitle}</h1>
          <Button asChild variant="outline">
            <Link href={`/bids/${bidId}/documents`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Documents
            </Link>
          </Button>
        </div>
        
        <p className="text-muted-foreground mt-1">
          {isOnline ? "Changes are saved automatically" : "Working offline - changes will sync when you reconnect"}
        </p>
      </header>
      
      <div className="flex-1">
        <DocumentEditor
          documentId={documentId}
          initialContent={documentContent}
          bidId={bidId}
          onSave={handleSaveContent}
          onVersionCreate={justCreatedVersion ? undefined : handleCreateVersion}
        />
      </div>
    </div>
  );
}
