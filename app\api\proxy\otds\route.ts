export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

  /**
   * Handles POST requests for OTDS authentication.
   *
   * This function processes a POST request to authenticate a user using OTDS (OpenText Directory Services).
   * It expects a JSON body with 'userName' and 'password' fields. If the credentials are valid, it validates the
   * AppWorks session associated with the authentication ticket and returns a success response with the user ID and
   * ticket information.
   *
   * @param {NextRequest} request - The incoming request object containing user credentials.
   * @returns {Promise<NextResponse>} - A response indicating the result of the authentication process.
   *
   * Possible response statuses:
   * - 200: Authentication and session validation are successful.
   * - 400: Missing user credentials.
   * - 401: Authentication or session validation failed.
   * - 500: An unexpected server error occurred.
   */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const otdsUrl = process.env.NEXT_PUBLIC_OTDS_AUTH_URL;
    
    if (!otdsUrl) {
      console.error('OTDS_AUTH_URL environment variable is not set');
      return NextResponse.json(
        { 
          status: 'error',
          code: 'CONFIG_ERROR',
          message: 'OTDS authentication URL is not configured'
        },
        { status: 500 }
      );
    }

    console.log('Attempting OTDS authentication with URL:', otdsUrl);
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    const response = await axios.post(otdsUrl, body, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      proxy: false,
      validateStatus: (status) => true, // Don't throw on any status
      timeout: 10000 // 10 second timeout
    });

    console.log('OTDS Raw Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    });

    // Handle non-200 responses from OTDS
    if (response.status !== 200) {
      console.error('OTDS server returned error status:', {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      // Check if we got HTML response (might indicate wrong URL or network issue)
      if (typeof response.data === 'string' && response.data.includes('<!DOCTYPE html>')) {
        return NextResponse.json(
          {
            status: 'error',
            code: 'INVALID_RESPONSE',
            message: 'OTDS server returned HTML instead of JSON. Please check the OTDS URL.'
          },
          { status: 500 }
        );
      }

      return NextResponse.json(
        {
          status: 'error',
          code: 'OTDS_ERROR',
          message: response.data?.message || `OTDS server error: ${response.statusText}`
        },
        { status: response.status }
      );
    }

    // Check for OTDS-specific error indicators
    if (response.data.failureReason) {
      console.error('OTDS authentication failed:', response.data.failureReason);
      return NextResponse.json(
        {
          status: 'error',
          code: 'OTDS_AUTH_FAILED',
          message: response.data.failureReason
        },
        { status: 401 }
      );
    }

    // Verify we got the required fields
    if (!response.data.ticket || !response.data.userId) {
      console.error('Invalid OTDS response - missing required fields:', response.data);
      return NextResponse.json(
        {
          status: 'error',
          code: 'INVALID_RESPONSE',
          message: 'Invalid response from OTDS server - missing required fields'
        },
        { status: 500 }
      );
    }

    console.log('OTDS authentication successful for user:', response.data.userId);
    return NextResponse.json({
      status: 'success',
      data: {
        userId: response.data.userId,
        ticket: process.env.NODE_ENV === 'development' ? response.data.ticket : undefined,
        ticketLength: response.data.ticket?.length,
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV
      }
    });
  } catch (error: any) {
    console.error('OTDS proxy error details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      config: {
        url: error.config?.url,
        method: error.config?.method,
        headers: error.config?.headers
      }
    });

    if (axios.isAxiosError(error)) {
      // Handle network or server errors
      if (!error.response) {
        return NextResponse.json(
          {
            status: 'error',
            code: 'NETWORK_ERROR',
            message: 'Could not connect to OTDS server'
          },
          { status: 503 }
        );
      }

      // Handle specific HTTP error codes
      switch (error.response.status) {
        case 401:
          return NextResponse.json(
            {
              status: 'error',
              code: 'INVALID_CREDENTIALS',
              message: 'Invalid username or password'
            },
            { status: 401 }
          );
        case 404:
          return NextResponse.json(
            {
              status: 'error',
              code: 'SERVER_NOT_FOUND',
              message: 'OTDS server not found'
            },
            { status: 404 }
          );
        default:
          return NextResponse.json(
            {
              status: 'error',
              code: 'SERVER_ERROR',
              message: 'OTDS server error'
            },
            { status: error.response.status || 500 }
          );
      }
    }
    console.error('OTDS proxy error:', error);
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Failed to communicate with OTDS service'
      },
      { status: 500 }
    );
  }
}
