export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { cookies } from 'next/headers';

/**
 * This is a proxy API endpoint to handle Appworks list API requests
 * It works around CORS issues by proxying the request from our server
 */
export async function POST(request: NextRequest) {
  try {
    // Get the entity type from query params and validate it
    const searchParams = request.nextUrl.searchParams;
    const entityType = searchParams.get('entityType');
    const listName = searchParams.get('listName') || 'DefaultList';
    
    if (!entityType) {
      return NextResponse.json({ error: 'Missing entityType parameter' }, { status: 400 });
    }
    
    // Extract the request body
    const requestBody = await request.json();
    
    // Get authorization token from cookies or headers
    const cookieStore = cookies();
    const sessionCookie = cookieStore.get('appSession');
    const authHeader = request.headers.get('Authorization');
    
    let samlToken = '';
    
    // First try to get token from Authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      samlToken = authHeader.substring(7);
    } 
    // If not in header, try to get from cookie
    else if (sessionCookie?.value) {
      try {
        const sessionData = JSON.parse(sessionCookie.value);
        if (sessionData?.samlArtifact) {
          samlToken = sessionData.samlArtifact;
        }
      } catch (e) {
        console.error('Error parsing session cookie:', e);
      }
    }
    
    if (!samlToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Construct the Appworks API URL
    const apiUrl = `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/${entityType}/lists/${listName}`;
    
    console.log(`Proxying request to Appworks: ${apiUrl}`);
    
    // Call the Appworks API
    const response = await axios.post(
      apiUrl,
      requestBody,
      {
        headers: {
          'Content-Type': 'application/json',
          'SAMLart': samlToken
        }
      }
    );
    
    // Return the Appworks response
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error in Appworks proxy API:', error);
    
    // Return a more detailed error response
    return NextResponse.json(
      { 
        error: 'Failed to retrieve data from Appworks',
        details: error.message,
        status: error.response?.status,
        data: error.response?.data
      },
      { status: error.response?.status || 500 }
    );
  }
}
