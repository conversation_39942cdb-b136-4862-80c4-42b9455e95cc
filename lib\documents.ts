import { DiffBlock, DocumentComment, DocumentStatus, DocumentVersion } from "@/types";
import { diff_match_patch } from 'diff-match-patch';

// Define the diff data structure to match our implementation
interface DiffResult {
  additions: DiffBlock[];
  deletions: DiffBlock[];
  modifications: DiffBlock[];
}

// DocumentVersionControl class for handling document versioning and diff operations
export class DocumentVersionControl {
  private differ: any;

  constructor() {
    this.differ = new diff_match_patch();
  }

  /**
   * Create a new document version
   */
  async createVersion(params: {
    documentId: string;
    content: string;
    previousVersion?: DocumentVersion;
    commitMessage: string;
    userId: string;
    userName: string;
    userRole?: string;
  }): Promise<DocumentVersion> {
    const { documentId, content, previousVersion, commitMessage, userId, userName, userRole = "User" } = params;
    
    // Generate diff if previous version exists
    let diff: string | undefined = undefined;
    let diffObj: DiffResult | undefined = undefined;
    
    if (previousVersion) {
      diffObj = this.calculateDiff(previousVersion.content, content);
      // Stringify the diff object to store it in the version record
      diff = JSON.stringify(diffObj);
    }
    
    // Generate new version number
    const newVersion = previousVersion 
      ? this.incrementVersion(previousVersion.version, this.determineChangeType(diffObj))
      : '1.0.0';
    
    const version: DocumentVersion = {
      id: crypto.randomUUID(),
      documentId,
      version: newVersion,
      content,
      previousVersionId: previousVersion?.id || null,
      diff,
      metadata: {
        createdAt: new Date().toISOString(),
        createdBy: {
          id: userId,
          name: userName,
          role: userRole
        },
        commitMessage,
        status: 'draft'
      }
    };

    return version;
  }

  /**
   * Determine the type of change (major, minor, patch) based on diff analysis
   */
  private determineChangeType(diff?: DiffResult): 'major' | 'minor' | 'patch' {
    if (!diff) return 'major'; // First version is major
    
    const totalChanges = 
      (diff.additions?.length || 0) + 
      (diff.deletions?.length || 0) + 
      (diff.modifications?.length || 0);
    
    // Simple logic: extensive changes = minor, small changes = patch
    // In a real system, you might want more sophisticated logic based on content analysis
    if (totalChanges > 50) return 'minor';
    return 'patch';
  }
  
  /**
   * Increment the version number based on change type
   */
  private incrementVersion(currentVersion: string, changeType: 'major' | 'minor' | 'patch'): string {
    const [major, minor, patch] = currentVersion.split('.').map(Number);
    
    switch (changeType) {
      case 'major':
        return `${major + 1}.0.0`;
      case 'minor':
        return `${major}.${minor + 1}.0`;
      case 'patch':
      default:
        return `${major}.${minor}.${patch + 1}`;
    }
  }

  /**
   * Calculate the diff between two content strings
   */
  private calculateDiff(oldContent: string, newContent: string): DiffResult {
    // Compute diff using diff-match-patch
    const diffs = this.differ.diff_main(oldContent, newContent);
    this.differ.diff_cleanupSemantic(diffs);
    
    // Extract diff blocks
    const additions: DiffBlock[] = [];
    const deletions: DiffBlock[] = [];
    const modifications: DiffBlock[] = [];
    
    let position = 0;
    
    diffs.forEach(([operation, text]: [number, string]) => {
      const length = text.length;
      
      // diff-match-patch uses: -1 = deletion, 0 = unchanged, 1 = addition
      switch (operation) {
        case -1: // Deletion
          deletions.push({
            startPosition: position,
            endPosition: position + length,
            content: text,
            type: 'deletion'
          });
          position += length;
          break;
          
        case 0: // Unchanged
          position += length;
          break;
          
        case 1: // Addition
          additions.push({
            startPosition: position,
            endPosition: position + length,
            content: text,
            type: 'addition'
          });
          
          // Check if this addition is close to a deletion (indicating a modification)
          if (deletions.length > 0) {
            const lastDeletion = deletions[deletions.length - 1];
            if (Math.abs(lastDeletion.endPosition - position) < 5) {
              modifications.push({
                startPosition: lastDeletion.startPosition,
                endPosition: position + length,
                content: `${lastDeletion.content} → ${text}`,
                type: 'modification'
              });
            }
          }
          break;
      }
    });
    
    return { additions, deletions, modifications };
  }
  
  /**
   * Compare two document versions and generate a diff
   */
  async compareVersions(v1: DocumentVersion, v2: DocumentVersion): Promise<DiffResult> {
    return this.calculateDiff(v1.content, v2.content);
  }
  
  /**
   * Revert to a specific version
   */
  async revertToVersion(params: {
    documentId: string;
    targetVersion: DocumentVersion;
    commitMessage: string;
    userId: string;
    userName: string;
    userRole?: string;
  }): Promise<DocumentVersion> {
    const { documentId, targetVersion, commitMessage, userId, userName, userRole } = params;
    
    // Get the latest version to properly increment from
    const latestVersion = await getLatestVersion(documentId);
    
    // Create a new version based on the content of the target version
    return this.createVersion({
      documentId,
      content: targetVersion.content,
      previousVersion: latestVersion,
      commitMessage: commitMessage || `Reverted to version ${targetVersion.version}`,
      userId,
      userName,
      userRole
    });
  }
}

// In-memory storage for document versions during development
const documentVersionsStore: Record<string, DocumentVersion[]> = {};

export const getLatestVersion = async (documentId: string): Promise<DocumentVersion | undefined> => {
  const versions = documentVersionsStore[documentId] || [];
  return versions.length > 0 ? versions[0] : undefined;
};

export const saveDocumentVersion = async (version: DocumentVersion): Promise<void> => {
  if (!documentVersionsStore[version.documentId]) {
    documentVersionsStore[version.documentId] = [];
  }
  
  // Add new version at the beginning of the array (latest first)
  documentVersionsStore[version.documentId].unshift(version);
};

export const getDocumentVersions = async (
  documentId: string, 
  options?: { limit?: number; offset?: number }
): Promise<DocumentVersion[]> => {
  const { limit = 10, offset = 0 } = options || {};
  const versions = documentVersionsStore[documentId] || [];
  
  return versions.slice(offset, offset + limit);
};

export const getDocumentVersion = async (versionId: string): Promise<DocumentVersion | undefined> => {
  // Search through all documents for the specified version
  for (const documentId in documentVersionsStore) {
    const version = documentVersionsStore[documentId].find(v => v.id === versionId);
    if (version) return version;
  }
  
  return undefined;
};

// In-memory storage for document comments during development
const documentCommentsStore: Record<string, DocumentComment[]> = {};

export const getDocumentComments = async (
  documentId: string,
  options?: { limit?: number; offset?: number; versionId?: string }
): Promise<DocumentComment[]> => {
  const { limit = 50, offset = 0, versionId } = options || {};
  let comments = documentCommentsStore[documentId] || [];
  
  if (versionId) {
    comments = comments.filter(comment => comment.versionId === versionId);
  }
  
  return comments.slice(offset, offset + limit);
};

export const saveDocumentComment = async (comment: DocumentComment): Promise<void> => {
  if (!documentCommentsStore[comment.documentId]) {
    documentCommentsStore[comment.documentId] = [];
  }
  
  documentCommentsStore[comment.documentId].push(comment);
};

export const updateDocumentStatus = async (
  documentId: string,
  versionId: string,
  status: DocumentStatus
): Promise<DocumentVersion | undefined> => {
  const versions = documentVersionsStore[documentId] || [];
  const versionIndex = versions.findIndex(v => v.id === versionId);
  
  if (versionIndex === -1) return undefined;
  
  const updatedVersion = {
    ...versions[versionIndex],
    metadata: {
      ...versions[versionIndex].metadata,
      status
    }
  };
  
  versions[versionIndex] = updatedVersion;
  return updatedVersion;
};
