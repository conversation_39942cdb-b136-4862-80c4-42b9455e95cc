import { NextResponse } from 'next/server';
import axios from 'axios';
import { headers } from 'next/headers';

export async function POST(request: Request) {
  try {
    // Get the SAML artifact from the request headers
    const headersList = headers();
    const samlArtifact = headersList.get('samlart');
    
    if (!samlArtifact) {
      console.error('No SAML artifact provided in request headers');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Parse request body to get any filters
    let requestBody = {};
    try {
      requestBody = await request.json();
    } catch (e) {
      // If parsing fails, use empty object
      console.warn('Failed to parse request body, using default filters');
    }

    // Use the exact format from the successful test
    const apiRequestBody = {
      distinct: 0,
      skip: 0,
      top: 0,
      parameters: {
        'Task.State': {
          name: "string",
          comparison: {
            value: "2",
            operator: "eq"
          }
        }
      }
    };

    // Use the correct endpoint that matches the successful test
    const apiEndpoint = `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/Bid/childEntities/LifecycleTask/lists/BidDefaultTaskList`;
    
    console.log('Forwarding request to Appworks API:', {
      url: apiEndpoint,
      body: apiRequestBody,
      hasSamlArtifact: !!samlArtifact
    });

    // Forward the request to the Appworks API
    const response = await axios({
      method: 'post',
      url: apiEndpoint,
      data: apiRequestBody,
      headers: {
        'Content-Type': 'application/json',
        'SAMLart': samlArtifact
      },
      maxRedirects: 5,
      validateStatus: () => true,
      timeout: 30000 // 30 seconds timeout to match other API calls
    });

    // Return the response from the Appworks API
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('API Error:', {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    console.error('Error in tasks API route:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tasks' },
      { status: 500 }
    );
  }
}
