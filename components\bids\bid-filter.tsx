"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { CalendarIcon, Filter, X } from "lucide-react";
import { BidFilterParams, Status } from "@/types";
import { cn } from "@/lib/utils";

interface BidFilterProps {
  onFilterChange: (filters: BidFilterParams) => void;
  onClearFilters: () => void;
  initialFilters?: BidFilterParams;
}

export function BidFilter({
  onFilterChange,
  onClearFilters,
  initialFilters,
}: BidFilterProps) {
  const [filters, setFilters] = useState<BidFilterParams>(initialFilters || {});
  const [createdDateFrom, setCreatedDateFrom] = useState<Date | undefined>(
    initialFilters?.createdDateFrom
      ? new Date(initialFilters.createdDateFrom)
      : undefined
  );
  const [createdDateTo, setCreatedDateTo] = useState<Date | undefined>(
    initialFilters?.createdDateTo
      ? new Date(initialFilters.createdDateTo)
      : undefined
  );

  const handleFilterChange = (
    key: keyof BidFilterParams,
    value: string | number | undefined
  ) => {
    const newFilters = { ...filters };

    // Handle "all" value as undefined (no filter)
    if (value === "all" || value === "" || value === undefined) {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleCreatedDateFromChange = (date: Date | undefined) => {
    setCreatedDateFrom(date);
    if (date) {
      const dateStr = date.toISOString();
      handleFilterChange("createdDateFrom", dateStr);
    } else {
      handleFilterChange("createdDateFrom", undefined);
    }
  };

  const handleCreatedDateToChange = (date: Date | undefined) => {
    setCreatedDateTo(date);
    if (date) {
      const dateStr = date.toISOString();
      handleFilterChange("createdDateTo", dateStr);
    } else {
      handleFilterChange("createdDateTo", undefined);
    }
  };

  const handleClearFilters = () => {
    setFilters({});
    setCreatedDateFrom(undefined);
    setCreatedDateTo(undefined);
    onClearFilters();
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className="flex flex-col gap-4 p-4 border rounded-lg bg-background shadow-sm">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filter Bids
        </h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            className="h-8 gap-1 text-muted-foreground hover:text-foreground"
          >
            <X className="h-3 w-3" />
            Clear Filters
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Title filter */}
        <div className="space-y-2">
          <Label htmlFor="title-filter">Title</Label>
          <Input
            id="title-filter"
            placeholder="Filter by title..."
            value={filters.title || ""}
            onChange={(e) => handleFilterChange("title", e.target.value)}
          />
        </div>

        {/* Status filter */}
        <div className="space-y-2">
          <Label htmlFor="status-filter">Status</Label>
          <Select
            value={filters.status || ""}
            onValueChange={(value) =>
              handleFilterChange("status", value || undefined)
            }
          >
            <SelectTrigger id="status-filter">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="DRAFT">Draft</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="APPROVED">Approved</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
              <SelectItem value="CLOSED">Closed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Opportunity Source filter */}
        <div className="space-y-2">
          <Label htmlFor="source-filter">Opportunity Source</Label>
          <Select
            value={filters.opportunitySource || ""}
            onValueChange={(value) =>
              handleFilterChange("opportunitySource", value || undefined)
            }
          >
            <SelectTrigger id="source-filter">
              <SelectValue placeholder="All sources" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All sources</SelectItem>
              <SelectItem value="rfp">RFP</SelectItem>
              <SelectItem value="direct">Direct</SelectItem>
              <SelectItem value="market">Market</SelectItem>
              <SelectItem value="internal">Internal</SelectItem>
              <SelectItem value="partner">Partner</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Strategic Alignment filter */}
        <div className="space-y-2">
          <Label htmlFor="alignment-filter">Strategic Alignment</Label>
          <Select
            value={filters.strategicAlignment || ""}
            onValueChange={(value) =>
              handleFilterChange("strategicAlignment", value || undefined)
            }
          >
            <SelectTrigger id="alignment-filter">
              <SelectValue placeholder="All alignments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All alignments</SelectItem>
              <SelectItem value="core">Core</SelectItem>
              <SelectItem value="expansion">Expansion</SelectItem>
              <SelectItem value="diversification">Diversification</SelectItem>
              <SelectItem value="innovation">Innovation</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Budget Min filter */}
        <div className="space-y-2">
          <Label htmlFor="budget-min-filter">Min Budget</Label>
          <Input
            id="budget-min-filter"
            type="number"
            placeholder="Min budget..."
            value={filters.budgetMin || ""}
            onChange={(e) =>
              handleFilterChange(
                "budgetMin",
                e.target.value ? Number(e.target.value) : undefined
              )
            }
          />
        </div>

        {/* Budget Max filter */}
        <div className="space-y-2">
          <Label htmlFor="budget-max-filter">Max Budget</Label>
          <Input
            id="budget-max-filter"
            type="number"
            placeholder="Max budget..."
            value={filters.budgetMax || ""}
            onChange={(e) =>
              handleFilterChange(
                "budgetMax",
                e.target.value ? Number(e.target.value) : undefined
              )
            }
          />
        </div>

        {/* Created Date From filter */}
        <div className="space-y-2">
          <Label htmlFor="date-from-filter">Created From</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date-from-filter"
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !createdDateFrom && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {createdDateFrom ? (
                  format(createdDateFrom, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={createdDateFrom}
                onSelect={handleCreatedDateFromChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Created Date To filter */}
        <div className="space-y-2">
          <Label htmlFor="date-to-filter">Created To</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date-to-filter"
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !createdDateTo && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {createdDateTo ? (
                  format(createdDateTo, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={createdDateTo}
                onSelect={handleCreatedDateToChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
    </div>
  );
}
