'use client';

import { use<PERSON>arams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useState } from 'react';
import { Loader2, CheckCircle2, AlertCircle } from 'lucide-react';

const evaluationSchema = z.object({
  technicalEvaluation: z.object({
    solutionMethodology: z.number().min(0).max(10),
    technicalCapability: z.number().min(0).max(10),
    innovationApproach: z.number().min(0).max(10),
    technicalNotes: z.string().min(1, 'Please provide technical evaluation notes'),
  }),
  commercialEvaluation: z.object({
    pricingStructure: z.number().min(0).max(10),
    costEffectiveness: z.number().min(0).max(10),
    financialStability: z.number().min(0).max(10),
    commercialNotes: z.string().min(1, 'Please provide commercial evaluation notes'),
  }),
  presentationEvaluation: z.object({
    clarity: z.number().min(0).max(10),
    understanding: z.number().min(0).max(10),
    qa_response: z.number().min(0).max(10),
    presentationNotes: z.string().min(1, 'Please provide presentation evaluation notes'),
  }),
  dueDiligence: z.object({
    referenceChecks: z.boolean(),
    legalCompliance: z.boolean(),
    financialVerification: z.boolean(),
    riskAssessment: z.boolean(),
    dueDiligenceNotes: z.string().min(1, 'Please provide due diligence notes'),
  }),
});

type EvaluationForm = z.infer<typeof evaluationSchema>;

export default function EvaluationPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('technical');

  const form = useForm<EvaluationForm>({
    resolver: zodResolver(evaluationSchema),
    defaultValues: {
      technicalEvaluation: {
        solutionMethodology: 5,
        technicalCapability: 5,
        innovationApproach: 5,
        technicalNotes: '',
      },
      commercialEvaluation: {
        pricingStructure: 5,
        costEffectiveness: 5,
        financialStability: 5,
        commercialNotes: '',
      },
      presentationEvaluation: {
        clarity: 5,
        understanding: 5,
        qa_response: 5,
        presentationNotes: '',
      },
      dueDiligence: {
        referenceChecks: false,
        legalCompliance: false,
        financialVerification: false,
        riskAssessment: false,
        dueDiligenceNotes: '',
      },
    },
  });

  const handleSubmit = async (data: EvaluationForm) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit evaluation
      console.log('Submitting evaluation:', data);
      
      // Calculate overall scores
      const technicalScore = (
        data.technicalEvaluation.solutionMethodology +
        data.technicalEvaluation.technicalCapability +
        data.technicalEvaluation.innovationApproach
      ) / 3;
      
      const commercialScore = (
        data.commercialEvaluation.pricingStructure +
        data.commercialEvaluation.costEffectiveness +
        data.commercialEvaluation.financialStability
      ) / 3;
      
      const presentationScore = (
        data.presentationEvaluation.clarity +
        data.presentationEvaluation.understanding +
        data.presentationEvaluation.qa_response
      ) / 3;

      toast({
        title: "Evaluation Submitted",
        description: "Vendor response evaluation has been submitted successfully.",
      });
    } catch (error) {
      console.error('Error submitting evaluation:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting the evaluation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderScoreSlider = (field: any, label: string) => (
    <FormItem className="space-y-4">
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <div className="flex items-center space-x-4">
          <Slider
            value={[field.value]}
            onValueChange={(value) => field.onChange(value[0])}
            max={10}
            step={1}
            className="w-full"
          />
          <span className="w-12 text-right">{field.value}/10</span>
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  );

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Vendor Response Evaluation</CardTitle>
          <CardDescription>
            Evaluate the vendor response for bid #{bidId} across technical, commercial, and presentation criteria.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="technical">Technical</TabsTrigger>
                  <TabsTrigger value="commercial">Commercial</TabsTrigger>
                  <TabsTrigger value="presentation">Presentation</TabsTrigger>
                  <TabsTrigger value="dueDiligence">Due Diligence</TabsTrigger>
                </TabsList>

                <TabsContent value="technical" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="technicalEvaluation.solutionMethodology"
                    render={({ field }) => renderScoreSlider(field, "Solution Methodology")}
                  />
                  <FormField
                    control={form.control}
                    name="technicalEvaluation.technicalCapability"
                    render={({ field }) => renderScoreSlider(field, "Technical Capability")}
                  />
                  <FormField
                    control={form.control}
                    name="technicalEvaluation.innovationApproach"
                    render={({ field }) => renderScoreSlider(field, "Innovation Approach")}
                  />
                  <FormField
                    control={form.control}
                    name="technicalEvaluation.technicalNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Technical Evaluation Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter detailed notes about the technical evaluation..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="commercial" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="commercialEvaluation.pricingStructure"
                    render={({ field }) => renderScoreSlider(field, "Pricing Structure")}
                  />
                  <FormField
                    control={form.control}
                    name="commercialEvaluation.costEffectiveness"
                    render={({ field }) => renderScoreSlider(field, "Cost Effectiveness")}
                  />
                  <FormField
                    control={form.control}
                    name="commercialEvaluation.financialStability"
                    render={({ field }) => renderScoreSlider(field, "Financial Stability")}
                  />
                  <FormField
                    control={form.control}
                    name="commercialEvaluation.commercialNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Commercial Evaluation Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter detailed notes about the commercial evaluation..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="presentation" className="space-y-6">
                  <FormField
                    control={form.control}
                    name="presentationEvaluation.clarity"
                    render={({ field }) => renderScoreSlider(field, "Presentation Clarity")}
                  />
                  <FormField
                    control={form.control}
                    name="presentationEvaluation.understanding"
                    render={({ field }) => renderScoreSlider(field, "Project Understanding")}
                  />
                  <FormField
                    control={form.control}
                    name="presentationEvaluation.qa_response"
                    render={({ field }) => renderScoreSlider(field, "Q&A Response Quality")}
                  />
                  <FormField
                    control={form.control}
                    name="presentationEvaluation.presentationNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Presentation Evaluation Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter detailed notes about the presentation evaluation..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="dueDiligence" className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="dueDiligence.referenceChecks"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4"
                            />
                          </FormControl>
                          <FormLabel className="!mt-0">Reference Checks Complete</FormLabel>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="dueDiligence.legalCompliance"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4"
                            />
                          </FormControl>
                          <FormLabel className="!mt-0">Legal Compliance Verified</FormLabel>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="dueDiligence.financialVerification"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4"
                            />
                          </FormControl>
                          <FormLabel className="!mt-0">Financial Verification Complete</FormLabel>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="dueDiligence.riskAssessment"
                      render={({ field }) => (
                        <FormItem className="flex items-center space-x-2">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4"
                            />
                          </FormControl>
                          <FormLabel className="!mt-0">Risk Assessment Complete</FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="dueDiligence.dueDiligenceNotes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Due Diligence Notes</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter detailed notes about the due diligence process..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              <div className="flex justify-end space-x-4">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Submit Evaluation
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
