import axios from 'axios';
import { getSAMLArtifact } from './samlUtils';
import { AppworksFilterParameter, Bid, BidFilterParams, FilterOperator } from '@/types';

/**
 * Interface for raw bid data as returned from Appworks API
 */
interface AppworksBidItem {
  _links: {
    item: {
      href: string;
    };
  };
  Properties: {
    Bid_Title: string;
    Bid_Description: string;
    Bid_Budget: string;
    Bid_Status: string;
    Bid_OpportunitySource: string;
    Bid_BusinessNeed: string;
    Bid_StrategicAlignment?: string;
    Bid_PotentialBenefits: string;
    Bid_PreliminaryScope: string;
    Bid_Stakeholders: string;
    Bid_RiskAssessment: string;
    Bid_BidStrategy: string;
    Bid_CompetitiveAnalysis: string;
    Bid_WinStrategy: string;
    Bid_TechnicalApproach: string;
    Bid_PricingStrategy: string;
    Bid_QualityStandards: string;
    Bid_ComplianceRequirements: string;
  };
}

interface AppworksBidListResponse {
  page: {
    skip: number;
    top: number;
    count: number;
  };
  _links: {
    self: {
      href: string;
    };
    first: {
      href: string;
    };
  };
  _embedded: {
    DefaultList: AppworksBidItem[];
  };
}

/**
 * Converts application filter parameters to Appworks API filter format
 * @param filters - The filter parameters from the application
 * @returns An object with Appworks-formatted filter parameters
 */
const buildAppworksFilterParameters = (filters?: BidFilterParams): Record<string, AppworksFilterParameter> => {
  if (!filters) return {};

  const parameters: Record<string, AppworksFilterParameter> = {};

  // Title filter (using Like operator for partial matches)
  if (filters.title) {
    parameters['Properties.Bid_Title'] = {
      name: 'Properties.Bid_Title',
      comparison: {
        value: `*${filters.title}*`, // Wildcard search
        operator: 'Like'
      }
    };
  }

  // Status filter (exact match)
  if (filters.status && filters.status !== 'all') {
    parameters['Properties.Bid_Status'] = {
      name: 'Properties.Bid_Status',
      comparison: {
        value: filters.status,
        operator: 'eq'
      }
    };
  }

  // Opportunity source filter (exact match)
  if (filters.opportunitySource && filters.opportunitySource !== 'all') {
    parameters['Properties.Bid_OpportunitySource'] = {
      name: 'Properties.Bid_OpportunitySource',
      comparison: {
        value: filters.opportunitySource,
        operator: 'eq'
      }
    };
  }

  // Strategic alignment filter (exact match)
  if (filters.strategicAlignment && filters.strategicAlignment !== 'all') {
    parameters['Properties.Bid_StrategicAlignment'] = {
      name: 'Properties.Bid_StrategicAlignment',
      comparison: {
        value: filters.strategicAlignment,
        operator: 'eq'
      }
    };
  }

  // Budget range filter (using Between operator)
  if (filters.budgetMin !== undefined && filters.budgetMax !== undefined) {
    parameters['Properties.Bid_Budget'] = {
      name: 'Properties.Bid_Budget',
      comparison: {
        from: filters.budgetMin.toString(),
        to: filters.budgetMax.toString(),
        operator: 'Between'
      }
    };
  } else if (filters.budgetMin !== undefined) {
    parameters['Properties.Bid_Budget'] = {
      name: 'Properties.Bid_Budget',
      comparison: {
        value: filters.budgetMin.toString(),
        operator: 'ge' // Greater than or equal
      }
    };
  } else if (filters.budgetMax !== undefined) {
    parameters['Properties.Bid_Budget'] = {
      name: 'Properties.Bid_Budget',
      comparison: {
        value: filters.budgetMax.toString(),
        operator: 'le' // Less than or equal
      }
    };
  }

  // Created date range filter (using Between operator)
  if (filters.createdDateFrom && filters.createdDateTo) {
    parameters['Tracking.CreatedDate'] = {
      name: 'Tracking.CreatedDate',
      comparison: {
        from: filters.createdDateFrom,
        to: filters.createdDateTo,
        operator: 'Between'
      }
    };
  } else if (filters.createdDateFrom) {
    parameters['Tracking.CreatedDate'] = {
      name: 'Tracking.CreatedDate',
      comparison: {
        value: filters.createdDateFrom,
        operator: 'ge' // Greater than or equal
      }
    };
  } else if (filters.createdDateTo) {
    parameters['Tracking.CreatedDate'] = {
      name: 'Tracking.CreatedDate',
      comparison: {
        value: filters.createdDateTo,
        operator: 'le' // Less than or equal
      }
    };
  }

  return parameters;
};

/**
 * Fetches bids from Appworks API through our proxy endpoint
 * @param filters - Optional filter parameters
 * @returns Promise with an array of Bid objects
 */
export const fetchBids = async (filters?: BidFilterParams): Promise<Bid[]> => {
  const samlArtifact = getSAMLArtifact();

  if (!samlArtifact) {
    throw new Error('Authentication token not found. Please log in again.');
  }

  try {
    // Build filter parameters if provided
    const parameters = buildAppworksFilterParameters(filters);

    const requestPayload = {
      distinct: 0,
      skip: 0,
      top: 100, // Fetch up to 100 bids
      parameters, // Apply filters if provided
      orderBy: [
        {
          name: 'Properties.Bid_Title',
          sortType: 'ASC'
        }
      ],
      select: [] // Empty select to get all fields
    };

    console.log('Fetching bids through proxy API with filters:', filters ? JSON.stringify(filters) : 'none');

    // Use our proxy API endpoint to avoid CORS issues
    const response = await axios.post<AppworksBidListResponse>(
      '/api/proxy/appworks/lists?entityType=Bid&listName=DefaultList',
      requestPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${samlArtifact.value}`
        }
      }
    );

    console.log('Response status:', response.status);
    console.log('Response data preview:',
      response.data && response.data._embedded ?
      `Found ${response.data._embedded.DefaultList?.length || 0} bids` :
      'No bids found in response');

    // Transform the Appworks response to our Bid interface format
    return transformAppworksBids(response.data);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error fetching bids from proxy API:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    } else {
      console.error('Unexpected error fetching bids:', error);
    }
    throw new Error('Failed to fetch bids. Please try again later.');
  }
};

/**
 * Transforms Appworks bid data to our application's Bid format
 */
const transformAppworksBids = (response: AppworksBidListResponse): Bid[] => {
  if (!response._embedded?.DefaultList) {
    return [];
  }

  return response._embedded.DefaultList.map(item => {
    // Extract the ID from the href
    const hrefParts = item._links.item.href.split('/');
    const bidId = hrefParts[hrefParts.length - 1];

    // Parse the bid data
    const bidData = item.Properties;

    return {
      bidId,
      title: bidData.Bid_Title || 'Untitled Bid',
      description: bidData.Bid_Description || '',
      budget: parseFloat(bidData.Bid_Budget) || 0,
      status: (bidData.Bid_Status as any) || 'DRAFT',
      dueDate: '', // Not available in the Appworks data
      createdAt: new Date().toISOString(), // Not available in the Appworks data
      updatedAt: new Date().toISOString(), // Not available in the Appworks data
      createdBy: '', // Not available in the Appworks data
      assignedTo: [], // Not available in the Appworks data
      documents: [], // Not available in the Appworks data
      comments: [], // Not available in the Appworks data
      // Add additional Appworks-specific fields
      opportunitySource: bidData.Bid_OpportunitySource || '',
      businessNeed: bidData.Bid_BusinessNeed || '',
      strategicAlignment: bidData.Bid_StrategicAlignment || '',
      potentialBenefits: bidData.Bid_PotentialBenefits || '',
      preliminaryScope: bidData.Bid_PreliminaryScope || '',
      stakeholders: bidData.Bid_Stakeholders || '',
      riskAssessment: bidData.Bid_RiskAssessment || '',
      bidStrategy: bidData.Bid_BidStrategy || '',
      competitiveAnalysis: bidData.Bid_CompetitiveAnalysis || '',
      winStrategy: bidData.Bid_WinStrategy || '',
      technicalApproach: bidData.Bid_TechnicalApproach || '',
      pricingStrategy: bidData.Bid_PricingStrategy || '',
      qualityStandards: bidData.Bid_QualityStandards || '',
      complianceRequirements: bidData.Bid_ComplianceRequirements || ''
    };
  });
};

/**
 * Updates an existing bid in the Appworks system
 * @param bidId - The ID of the bid to update
 * @param bidData - The updated bid data
 * @returns Promise with a success boolean
 */
export const updateBid = async (bidId: string, bidData: Partial<Bid>): Promise<boolean> => {
  const samlArtifact = getSAMLArtifact();

  if (!samlArtifact) {
    throw new Error('Authentication token not found. Please log in again.');
  }

  try {
    // Prepare the properties object for the Appworks API
    const properties: Record<string, string | number> = {
      // Convert our model to the Appworks property format
      Bid_Title: bidData.title || '',
      Bid_Description: bidData.description || '',
      Bid_Budget: typeof bidData.budget === 'number' ? bidData.budget : 0,
      Bid_Status: bidData.status || 'DRAFT',
      Bid_OpportunitySource: bidData.opportunitySource || '',
      Bid_BusinessNeed: bidData.businessNeed || '',
      Bid_PotentialBenefits: bidData.potentialBenefits || '',
      Bid_PreliminaryScope: bidData.preliminaryScope || '',
      Bid_Stakeholders: bidData.stakeholders || '',
      Bid_RiskAssessment: bidData.riskAssessment || '',
      Bid_BidStrategy: bidData.bidStrategy || '',
      Bid_CompetitiveAnalysis: bidData.competitiveAnalysis || '',
      Bid_WinStrategy: bidData.winStrategy || '',
      Bid_TechnicalApproach: bidData.technicalApproach || '',
      Bid_PricingStrategy: bidData.pricingStrategy || '',
      Bid_QualityStandards: bidData.qualityStandards || '',
      Bid_ComplianceRequirements: bidData.complianceRequirements || '',
      Bid_StrategicAlignment: bidData.strategicAlignment || '',
      // If due date is provided, format it properly
      ...(bidData.dueDate && { Bid_DueDate: bidData.dueDate })
    };

    console.log(`Updating bid with ID: ${bidId}`);

    // Use our proxy API endpoint to avoid CORS issues
    // The correct URL format is /api/proxy/appworks/entities/Bid/items/{id}
    const response = await axios.put(
      `/api/proxy/appworks/entities/Bid/items/${bidId}`,
      { Properties: properties },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${samlArtifact.value}`
        }
      }
    );

    // API returns 204 No Content on success
    return response.status === 204;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error updating bid in Appworks:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    } else {
      console.error('Unexpected error updating bid:', error);
    }
    throw new Error('Failed to update bid. Please try again later.');
  }
};
