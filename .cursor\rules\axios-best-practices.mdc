---
description: 
globs: 
alwaysApply: true
---
description: Best practices for using Axios in HTTP requests
globs: **/*.{ts,tsx,js,jsx}
---

- Use interceptors for global request/response handling
- Implement proper error handling with try/catch blocks
- Set appropriate timeouts to prevent hanging requests
- Use cancel tokens for aborting requests when needed
- Implement request and response transformation for data consistency