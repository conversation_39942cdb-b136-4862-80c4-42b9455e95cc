"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { mockUsers } from "@/lib/mock-data";
import { Bid } from "@/types";
import { FileText, MessageSquare, Paperclip, Send, Loader2, AlertCircle, Folder, FilePlus } from "lucide-react";
import { useEffect, useState } from "react";
import { fetchBids } from "@/lib/api";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { Dialog, DialogContent, <PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { EditBidDialog } from "@/components/bids/edit-bid-dialog";

export default function BidDetailsClient({
  bidId,
}: {
  bidId: string;
}) {
  const [bid, setBid] = useState<Bid | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [newComment, setNewComment] = useState("");
  const { user } = useAuth();

  // State for document creation
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newDocTitle, setNewDocTitle] = useState("");
  const [newDocDescription, setNewDocDescription] = useState("");
  const router = useRouter();
  const { toast } = useToast();

  // State for edit bid dialog
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  useEffect(() => {
    const getBid = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const bids = await fetchBids();
        const foundBid = bids.find((b) => b.bidId === bidId);

        if (foundBid) {
          setBid(foundBid);
        } else {
          setError("Bid not found");
        }
      } catch (err) {
        console.error("Error fetching bid details:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch bid details");
      } finally {
        setIsLoading(false);
      }
    };

    if (user) {
      getBid();
    } else {
      setIsLoading(false);
      setError("Authentication required");
    }
  }, [bidId, user]);

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    // This will be replaced with actual API call to OpenText Appworks
    console.log("Adding comment:", newComment);
    setNewComment("");
  };

  // Document creation handler
  const handleCreateDocument = () => {
    setIsCreateDialogOpen(true);
  };

  // Edit bid handler
  const handleEditBid = () => {
    setIsEditDialogOpen(true);
  };

  // Handle bid update from edit dialog
  const handleBidUpdated = (updatedBid: Bid) => {
    // Update the bid in the state
    setBid(updatedBid);

    toast({
      title: "Bid updated",
      description: "The bid has been successfully updated",
    });
  };

  // Document creation handler
  const handleCreateDocumentSubmit = async () => {
    if (!newDocTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a document title",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      // Generate a unique document ID
      const documentId = crypto.randomUUID();

      // Create a document record in IndexedDB
      const openRequest = indexedDB.open("BidManagementDB", 1);

      openRequest.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains("documents")) {
          const documentStore = db.createObjectStore("documents", { keyPath: "id" });
          documentStore.createIndex("bidId", "bidId", { unique: false });
        }

        if (!db.objectStoreNames.contains("versions")) {
          const versionStore = db.createObjectStore("versions", { keyPath: "id" });
          versionStore.createIndex("documentId", "documentId", { unique: false });
        }

        if (!db.objectStoreNames.contains("comments")) {
          const commentStore = db.createObjectStore("comments", { keyPath: "id" });
          commentStore.createIndex("documentId", "documentId", { unique: false });
        }
      };

      openRequest.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const transaction = db.transaction("documents", "readwrite");
        const store = transaction.objectStore("documents");

        const now = new Date().toISOString();

        const document = {
          id: documentId,
          bidId,
          title: newDocTitle,
          description: newDocDescription,
          content: "", // Start with empty content
          status: "draft",
          createdAt: now,
          updatedAt: now,
          createdBy: {
            id: user?.userId || "unknown",
            name: user?.displayName || user?.userId || "Anonymous",
            role: "User"
          }
        };

        const request = store.add(document);

        request.onsuccess = () => {
          // Reset form
          setNewDocTitle("");
          setNewDocDescription("");
          setIsCreateDialogOpen(false);

          toast({
            title: "Document created",
            description: "Your new document has been created"
          });

          // Verify document was created before redirecting
          const verifyTransaction = db.transaction("documents", "readonly");
          const verifyStore = verifyTransaction.objectStore("documents");
          const verifyRequest = verifyStore.get(documentId);

          verifyRequest.onsuccess = () => {
            if (verifyRequest.result) {
              console.log("Document created successfully:", verifyRequest.result);
              // Document exists, safe to navigate
              setTimeout(() => {
                router.push(`/bids/${bidId}/documents/${documentId}`);
              }, 300); // Small delay to ensure IndexedDB has completed all operations
            } else {
              console.error("Document was not found after creation");
              toast({
                title: "Error",
                description: "Document was created but could not be loaded. Please refresh the documents list.",
                variant: "destructive",
              });
              setIsCreating(false);
            }
          };

          verifyRequest.onerror = () => {
            console.error("Error verifying document creation");
            // Navigate anyway, but there might be issues
            router.push(`/bids/${bidId}/documents/${documentId}`);
          };
        };

        request.onerror = (e) => {
          console.error("Error adding document:", e);
          toast({
            title: "Error",
            description: "Failed to create document",
            variant: "destructive",
          });
          setIsCreating(false);
        };

        transaction.oncomplete = () => {
          // Don't close the DB until the verification is done
        };
      };

      openRequest.onerror = (e) => {
        console.error("Error opening database:", e);
        toast({
          title: "Error",
          description: "Could not access the document database",
          variant: "destructive",
        });
        setIsCreating(false);
      };
    } catch (error) {
      console.error("Error creating document:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      setIsCreating(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 text-center">
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading bid details...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <div className="text-center mt-4">
          <Link href="/bids" className="text-blue-600 hover:underline">
            Return to bids list
          </Link>
        </div>
      </div>
    );
  }

  // Bid not found state
  if (!bid) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Bid not found</h2>
        <p className="mb-4">The bid you&apos;re looking for could not be found.</p>
        <Link href="/bids" className="text-blue-600 hover:underline">
          Return to bids list
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-2xl">{bid.title}</CardTitle>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge>{bid.status}</Badge>
                    <span className="text-sm text-muted-foreground">
                      Created on {new Date(bid.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' })}
                    </span>
                  </div>
                </div>
                <Button variant="outline" onClick={handleEditBid}>Edit Bid</Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p>{bid.description}</p>
              </div>

              <Separator className="my-6" />

              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">Documents</h3>
                  <Link href={`/bids/${bidId}/documents`} tabIndex={0} aria-label="View all documents">
                    <Button variant="outline" size="sm" className="flex gap-2">
                      <Folder className="h-4 w-4" />
                      View All Documents
                    </Button>
                  </Link>
                </div>
                <div className="space-y-2">
                  {bid.documents && bid.documents.length > 0 ? (
                    bid.documents.slice(0, 3).map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-3 hover:bg-accent rounded-md border"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span>{doc.name}</span>
                        </div>
                        <div className="flex gap-2">
                          <Link href={`/bids/${bidId}/documents/${doc.id}`} tabIndex={0} aria-label={`Edit ${doc.name}`}>
                            <Button variant="outline" size="sm">
                              Edit
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted-foreground mb-4">No documents attached</p>
                  )}
                  {bid.documents && bid.documents.length > 3 && (
                    <div className="text-center mt-2">
                      <Link href={`/bids/${bidId}/documents`} className="text-sm text-primary hover:underline">
                        View all {bid.documents.length} documents
                      </Link>
                    </div>
                  )}
                  <div className="flex justify-between mt-4">
                    <Button variant="outline" className="flex gap-2">
                      <Paperclip className="h-4 w-4" />
                      Attach Existing Document
                    </Button>
                    <Button onClick={handleCreateDocument} className="flex gap-2">
                      <FilePlus className="h-4 w-4" />
                      Create New Document
                    </Button>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div>
                <h3 className="text-lg font-semibold mb-4">Comments</h3>
                <div className="space-y-4">
                  {bid.comments && bid.comments.length > 0 ? (
                    bid.comments.map((comment) => {
                      // TODO: Replace with real user data from API when available
                      const user = mockUsers.find((u) => u.id === comment.createdBy);
                      return (
                        <div key={comment.id} className="flex gap-4">
                          <Avatar>
                            <AvatarImage src={user?.avatar} />
                            <AvatarFallback>
                              {user?.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-semibold">{user?.name}</span>
                              <span className="text-sm text-muted-foreground">
                                {new Date(comment.createdAt).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' })}
                              </span>
                            </div>
                            <p className="mt-1">{comment.content}</p>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-muted-foreground">No comments yet</p>
                  )}
                </div>

                <div className="mt-4 flex gap-2">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                  />
                  <Button onClick={handleAddComment}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Bid Details</CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="space-y-4">
                <div>
                  <dt className="text-sm text-muted-foreground">Budget</dt>
                  <dd className="text-lg font-semibold">
                    ${bid.budget.toLocaleString()}
                  </dd>
                </div>
                {bid.dueDate && (
                  <div>
                    <dt className="text-sm text-muted-foreground">Due Date</dt>
                    <dd className="text-lg font-semibold">
                      {new Date(bid.dueDate).toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' })}
                    </dd>
                  </div>
                )}
                <Separator />
                <div>
                  <dt className="text-sm text-muted-foreground mb-2">
                    Assigned To
                  </dt>
                  <dd>
                    <div className="flex flex-wrap gap-2">
                      {bid.assignedTo && bid.assignedTo.length > 0 ? (
                        bid.assignedTo.map((userId) => {
                          const user = mockUsers.find((u) => u.id === userId);
                          return (
                            <div
                              key={userId}
                              className="flex items-center gap-2 bg-accent p-2 rounded-md"
                            >
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={user?.avatar} />
                                <AvatarFallback>
                                  {user?.name.charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-sm">{user?.name}</span>
                            </div>
                          );
                        })
                      ) : (
                        <p className="text-muted-foreground">No users assigned</p>
                      )}
                      <Button variant="outline" size="sm" className="w-full">
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Assign Users
                      </Button>
                    </div>
                  </dd>
                </div>
              </dl>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Document creation dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Document</DialogTitle>
            <DialogDescription>
              Add a new document to this bid. You can edit the content after creation.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Document Title</Label>
              <Input
                id="title"
                placeholder="Enter document title"
                value={newDocTitle}
                onChange={(e) => setNewDocTitle(e.target.value)}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                placeholder="Enter document description"
                value={newDocDescription}
                onChange={(e) => setNewDocDescription(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsCreateDialogOpen(false)}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateDocumentSubmit}
              disabled={isCreating}
            >
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Document"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Bid Dialog */}
      {bid && (
        <EditBidDialog
          bid={bid}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          onBidUpdated={handleBidUpdated}
        />
      )}
    </div>
  );
}