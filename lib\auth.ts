import axios from 'axios';
import { extractSAMLArtifact, storeSAMLArtifact } from './samlUtils';

export interface OTDSAuthResponse {
  token: string;
  userId: string;
  ticket: string;
  resourceID: string | null;
  failureReason: string | null;
  passwordExpirationTime: number;
  continuation: boolean;
  continuationContext: string | null;
  continuationData: string | null;
  displayName?: string; // Optional display name for the user
  role?: string; // Optional user role
}

export interface AuthCredentials {
  userName: string;
  password: string;
}

export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

  /**
   * Authenticate with OTDS.
   *
   * @param credentials - The credentials to use for authentication.
   * @returns The OTDS authentication response.
   * @throws AuthenticationError - If authentication fails.
   */
/**
 * Authenticates with the OTDS server using the provided credentials.
 *
 * @param credentials - The authentication credentials containing userName and password.
 * @returns A promise that resolves to the OTDSAuthResponse, which includes the authentication token and user details.
 * @throws AuthenticationError - If the authentication fails due to invalid credentials or server issues.
 */
export const authenticateWithOTDS = async (credentials: AuthCredentials): Promise<OTDSAuthResponse> => {
  try {
    const response = await axios.post<OTDSAuthResponse>(
      process.env.NEXT_PUBLIC_OTDS_AUTH_URL!,
      credentials,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    // Log the OTDS response for debugging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('OTDS Authentication Response:', {
        userId: response.data.userId,
        ticket: response.data.ticket,
        ticketLength: response.data.ticket?.length,
        timestamp: new Date().toISOString()
      });
    }

    if (response.data.failureReason) {
      throw new AuthenticationError(response.data.failureReason);
    }

    return response.data;
  } catch (error) {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    throw new AuthenticationError('Failed to authenticate with OTDS');
  }
};

/**
 * Constructs a SOAP header containing the OTAuthentication token.
 * 
 * @param otdsTicket - The OTDS authentication token used for securing
 *                     communication with the AppWorks server.
 * @returns A string representing the SOAP header with the authentication token.
 */
export const createAppWorksSoapHeader = (otdsTicket: string): string => {
  return `
    <SOAP:Header>
      <OTAuthentication xmlns="urn:api.bpm.opentext.com">
        <AuthenticationToken>${otdsTicket}</AuthenticationToken>
      </OTAuthentication>
    </SOAP:Header>
  `;
};

  /**
   * Validates an AppWorks session using the OTDS authentication ticket.
   * 
   * @param otdsTicket - The OTDS authentication ticket obtained from the OTDS server.
   * 
   * @returns A boolean indicating whether the AppWorks session is valid or not.
   */
export const validateAppWorksSession = async (otdsTicket: string): Promise<boolean> => {
  const soapEnvelope = `
    <SOAP:Envelope xmlns:SOAP="http://schemas.xmlsoap.org/soap/envelope/">
      ${createAppWorksSoapHeader(otdsTicket)}
      <SOAP:Body>
        <samlp:Request xmlns:samlp="urn:oasis:names:tc:SAML:1.0:protocol" 
                      MajorVersion="1" 
                      MinorVersion="1" 
                      IssueInstant="${new Date().toISOString()}" 
                      RequestID="${Date.now()}">
          <samlp:AuthenticationQuery>
            <saml:Subject xmlns:saml="urn:oasis:names:tc:SAML:1.0:assertion">
              <saml:NameIdentifier Format="urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"/>
            </saml:Subject>
          </samlp:AuthenticationQuery>
        </samlp:Request>
      </SOAP:Body>
    </SOAP:Envelope>
  `;

  try {
    // Use the AppWorks URL directly when running on server side
    const isServer = typeof window === 'undefined';
    const baseUrl = isServer 
      ? process.env.NEXT_PUBLIC_APPWORKS_AUTH_URL 
      : '';
    
    console.log('Validating AppWorks session with OTDS ticket:', {
      ticketLength: otdsTicket.length,
      ticketPreview: `${otdsTicket.substring(0, 20)}...${otdsTicket.substring(otdsTicket.length - 20)}`,
      isServer,
      url: isServer ? baseUrl : '/api/proxy/appworks'
    });

    const response = await axios.post(
      isServer ? baseUrl! : '/api/proxy/appworks',
      soapEnvelope,
      {
        headers: {
          'Content-Type': 'text/xml',
          'SOAPAction': 'urn:oasis:names:tc:SAML:1.0:protocol:AuthenticationQuery'
        }
      }
    );

    const responseData = response.data;

    // Debug the response
    console.log('\n=== Debug SAML Response ===');
    console.log('Response type:', typeof responseData);
    console.log('Response length:', typeof responseData === 'string' ? responseData.length : 'N/A');
    console.log('First 200 chars:', typeof responseData === 'string' ? responseData.substring(0, 200) : 'N/A');
    console.log('=== End Debug ===\n');

    // Extract the SAML artifact using the exact XML path
    if (typeof responseData === 'string') {
      const match = responseData.match(/<samlp:AssertionArtifact[^>]*>([^]*?)<\/samlp:AssertionArtifact>/);
      if (match && match[1]) {
        const artifact = match[1].trim();
        console.log('\n=== SAML AssertionArtifact ===');
        console.log(artifact);
        console.log('=== End AssertionArtifact ===\n');

        // Store artifact if on client side
        if (!isServer) {
          storeSAMLArtifact(artifact);
        }
      } else {
        console.warn('No AssertionArtifact found in SAML response');
      }
    }

    return response.status === 200;
  } catch (error) {
    console.error('Failed to validate AppWorks session:', error);
    return false;
  }
};
