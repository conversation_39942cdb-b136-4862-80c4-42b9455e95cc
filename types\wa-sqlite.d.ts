declare module 'wa-sqlite' {
  // Main SQLite API
  export const SQLITE_OK: number;
  export const SQLITE_ROW: number;
  export const SQLITE_DONE: number;
  
  // Default export for loading the WASM module
  export default function(): Promise<any>;
  
  // Factory function to create SQLite API
  export function Factory(module: any): any;
}

declare module 'wa-sqlite/src/examples/IDBBatchAtomicVFS' {
  export class IDBBatchAtomicVFS {
    constructor(name: string, options?: { durability?: string });
  }
}

declare module 'wa-sqlite/src/examples/OPFSAdaptiveVFS' {
  export class OPFSAdaptiveVFS {
    constructor();
  }
} 