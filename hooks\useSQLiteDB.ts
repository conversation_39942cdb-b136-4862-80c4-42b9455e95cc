"use client";

import { useState, useEffect, useCallback } from 'react';
import * as SQLiteDB from '@/lib/sqlitedb';
import { DocumentVersion, DocumentComment } from '@/types';
import { useToast } from "@/components/ui/use-toast";

export function useSQLiteDB(documentId?: string, bidId?: string) {
  const [isDBAvailable, setIsDBAvailable] = useState<boolean | null>(null);
  const [isOnline, setIsOnline] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [document, setDocument] = useState<any | null>(null);
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [comments, setComments] = useState<DocumentComment[]>([]);
  const { toast } = useToast();

  // Initialize and check SQLite availability
  useEffect(() => {
    const checkAvailability = async () => {
      try {
        const available = SQLiteDB.isSQLiteAvailable();
        setIsDBAvailable(available);

        if (available) {
          // Initialize the database
          await SQLiteDB.initDatabase();
        } else {
          toast({
            title: "Limited functionality",
            description: "Your browser doesn't support offline document editing.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error checking SQLite availability:', error);
        setIsDBAvailable(false);
      }
    };

    checkAvailability();

    // Set up online/offline detection
    const handleOnline = () => {
      setIsOnline(true);
      toast({
        title: "Back online",
        description: "Your changes will be synced to the server.",
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast({
        title: "You're offline",
        description: "Changes will be saved locally until you reconnect.",
      });
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    setIsOnline(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [toast]);

  // Load document data when documentId changes
  useEffect(() => {
    if (documentId && isDBAvailable) {
      loadDocument(documentId);
      loadVersions(documentId);
      loadComments(documentId);
    }
  }, [documentId, isDBAvailable]);

  // Load document function
  const loadDocument = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const doc = await SQLiteDB.getDocument(docId);
      
      // If bidId is provided, verify the document belongs to the bid
      if (doc && bidId) {
        if (doc.bidId && doc.bidId !== bidId) {
          console.warn(`Document ${docId} belongs to bid ${doc.bidId}, not ${bidId}`);
          setDocument(null);
          setIsLoading(false);
          return;
        }
        
        // If document doesn't have a bidId, update it
        if (!doc.bidId) {
          const updatedDoc = { ...doc, bidId };
          await SQLiteDB.saveDocument(updatedDoc);
          setDocument(updatedDoc);
        } else {
          setDocument(doc);
        }
      } else {
        setDocument(doc);
      }
    } catch (error) {
      console.error("Error loading document:", error);
      toast({
        title: "Error",
        description: "Failed to load document from local storage.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast, bidId]);

  // Load versions function
  const loadVersions = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const docVersions = await SQLiteDB.getDocumentVersions(docId);
      setVersions(docVersions);
    } catch (error) {
      console.error("Error loading document versions:", error);
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable]);

  // Load comments function
  const loadComments = useCallback(async (docId: string) => {
    if (!isDBAvailable) return;
    
    setIsLoading(true);
    try {
      const docComments = await SQLiteDB.getDocumentComments(docId);
      setComments(docComments);
    } catch (error) {
      console.error("Error loading document comments:", error);
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable]);

  // Save document function
  const saveDocument = useCallback(async (doc: any) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Make sure bidId is set if available
      if (bidId && !doc.bidId) {
        doc.bidId = bidId;
      }
      
      await SQLiteDB.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document:", error);
      toast({
        title: "Error",
        description: "Failed to save document to local storage.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, toast, bidId]);

  // Save document content
  const saveDocumentContent = useCallback(async (docId: string, content: string) => {
    if (!isDBAvailable || !docId) return null;
    
    setIsLoading(true);
    try {
      // Get existing document or create a new one
      let doc = await SQLiteDB.getDocument(docId);
      
      if (!doc) {
        doc = {
          id: docId,
          bidId: bidId, // Set bidId for new documents
          content,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      } else {
        doc.content = content;
        doc.updatedAt = new Date().toISOString();
        
        // Update bidId if needed
        if (bidId && !doc.bidId) {
          doc.bidId = bidId;
        }
      }
      
      // Save unsaved edit if offline
      if (!isOnline) {
        await SQLiteDB.saveDocumentEdit({
          id: crypto.randomUUID(),
          documentId: docId,
          content,
          timestamp: new Date().toISOString()
        });
        
        toast({
          title: "Saved offline",
          description: "Your changes are saved locally and will sync when you're back online.",
        });
      }
      
      await SQLiteDB.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document content:", error);
      toast({
        title: "Error",
        description: "Failed to save document content locally.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, isOnline, toast, bidId]);

  // Create a new document version
  const createVersion = useCallback(async (params: {
    documentId: string;
    content: string;
    commitMessage: string;
    userId: string;
    userName: string;
    userRole?: string;
  }) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Get latest version to use as previous version
      const previousVersion = await SQLiteDB.getLatestVersion(params.documentId);
      
      // Create version control service instance
      const { DocumentVersionControl } = await import('@/lib/documents');
      const versionControl = new DocumentVersionControl();
      
      // Create the new version
      const newVersion = await versionControl.createVersion({
        documentId: params.documentId,
        content: params.content,
        previousVersion: previousVersion || undefined,
        commitMessage: params.commitMessage,
        userId: params.userId,
        userName: params.userName,
        userRole: params.userRole
      });
      
      // Save to SQLite
      await SQLiteDB.saveDocumentVersion(newVersion);
      
      // Refresh versions list
      await loadVersions(params.documentId);
      
      return newVersion;
    } catch (error) {
      console.error("Error creating version:", error);
      toast({
        title: "Error",
        description: "Failed to create document version.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, loadVersions, toast]);

  // Restore a document version
  const restoreVersion = useCallback(async (versionId: string) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Get the version to restore
      const version = await SQLiteDB.getDocumentVersion(versionId);
      if (!version) {
        throw new Error(`Version ${versionId} not found`);
      }
      
      // Update document with this version's content
      const doc = await SQLiteDB.getDocument(version.documentId);
      if (doc) {
        doc.content = version.content;
        doc.updatedAt = new Date().toISOString();
        doc.restoredFromVersionId = versionId;
        
        // Save the document
        await SQLiteDB.saveDocument(doc);
        setDocument(doc);
        
        // Save a new version to mark the restoration
        if (documentId) {
          await createVersion({
            documentId: version.documentId,
            content: version.content,
            commitMessage: `Restored from version ${version.version}`,
            userId: version.metadata?.createdBy?.id || 'system',
            userName: version.metadata?.createdBy?.name || 'System',
            userRole: 'User'
          });
        }
        
        return doc;
      }
      return null;
    } catch (error) {
      console.error("Error restoring version:", error);
      toast({
        title: "Error",
        description: "Failed to restore document version.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, createVersion, documentId, toast]);

  // Create a comment on the document
  const createComment = useCallback(async (params: {
    documentId: string;
    content: string;
    author: {
      id: string;
      name: string;
      avatar?: string;
    };
    selection?: {
      from: number;
      to: number;
    };
    versionId?: string;
  }) => {
    if (!isDBAvailable) return null;
    
    setIsLoading(true);
    try {
      // Create a new comment
      const newComment = {
        id: crypto.randomUUID(),
        documentId: params.documentId,
        versionId: params.versionId,
        content: params.content,
        author: params.author,
        createdAt: new Date().toISOString(),
        selection: params.selection,
        resolved: false
      };
      
      // Save to SQLite
      await SQLiteDB.saveDocumentComment(newComment);
      
      // Refresh comments list
      await loadComments(params.documentId);
      
      return newComment;
    } catch (error) {
      console.error("Error creating comment:", error);
      toast({
        title: "Error",
        description: "Failed to create comment.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, loadComments, toast]);

  // Sync offline changes when back online
  const syncOfflineChanges = useCallback(async () => {
    if (!isDBAvailable || !documentId || !isOnline) return;
    
    setIsLoading(true);
    try {
      // Get all pending edits
      const pendingEdits = await SQLiteDB.getDocumentEdits(documentId);
      
      if (pendingEdits.length === 0) return;
      
      // This is where you would typically sync with server
      console.log(`Syncing ${pendingEdits.length} pending edits to server`);
      
      // Create a new version with the latest content
      if (document && pendingEdits.length > 0) {
        // Save version with latest content
        await createVersion({
          documentId,
          content: document.content,
          commitMessage: `Synced ${pendingEdits.length} offline edits`,
          userId: 'sync-system',
          userName: 'System (Sync)',
        });
        
        // Clear pending edits
        await SQLiteDB.clearDocumentEdits(documentId);
        
        toast({
          title: "Sync complete",
          description: `Synced ${pendingEdits.length} offline changes.`,
        });
      }
    } catch (error) {
      console.error("Error syncing offline changes:", error);
      toast({
        title: "Sync Error",
        description: "Failed to sync offline changes. Try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isDBAvailable, documentId, isOnline, document, createVersion, toast]);

  // Auto-sync when online status changes
  useEffect(() => {
    if (isOnline && documentId) {
      syncOfflineChanges();
    }
  }, [isOnline, documentId, syncOfflineChanges]);

  return {
    isDBAvailable,
    isOnline,
    isLoading: isLoading,
    document: document,
    versions,
    comments,
    saveDocument,
    saveDocumentContent,
    loadDocument,
    createVersion,
    restoreVersion,
    createComment,
    syncOfflineChanges
  };
} 