import * as IndexedDB from '../indexeddb';
import sqlitedb from '../sqlitedb';
import { Document } from '../../types/sqlitedb';
import { DocumentComment, DocumentVersion } from '../../types/index';

// IndexedDB store names from indexeddb.ts
const DOCUMENT_STORE = 'documents';
const VERSION_STORE = 'documentVersions';
const COMMENT_STORE = 'documentComments';
const EDIT_STORE = 'documentEdits';

/**
 * Migrates all data from IndexedDB to SQLite
 */
export async function migrateToSQLite(): Promise<{ success: boolean, message: string }> {
  try {
    console.log('Starting migration from IndexedDB to SQLite...');
    
    // Open IndexedDB using the internal function
    const idbDatabase = await getIndexedDBConnection();
    
    // Debug the IndexedDB structure
    await debugIndexedDBStructure(idbDatabase);
    
    await sqlitedb.initSQLite();
    
    // Migrate documents first and ensure they're all processed
    console.log('Step 1: Migrating documents...');
    const migratedDocs = await migrateDocuments(idbDatabase);
    console.log(`Migrated ${migratedDocs} documents`);
    
    // If no documents were migrated but documents exist in IndexedDB, stop and report an error
    if (migratedDocs === 0) {
      const docCount = await getStoreCount(idbDatabase, DOCUMENT_STORE);
      if (docCount > 0) {
        return {
          success: false,
          message: `Failed to migrate documents: Found ${docCount} documents in IndexedDB but none were migrated.`
        };
      }
    }
    
    // Migrate document versions - with our recent fix, this will create placeholder documents if needed
    console.log('Step 2: Migrating document versions...');
    const migratedVersions = await migrateDocumentVersions(idbDatabase);
    console.log(`Migrated ${migratedVersions} document versions`);
    
    // Migrate document comments
    console.log('Step 3: Migrating document comments...');
    const migratedComments = await migrateDocumentComments(idbDatabase);
    console.log(`Migrated ${migratedComments} document comments`);
    
    // Migrate document edits
    console.log('Step 4: Migrating document edits...');
    const migratedEdits = await migrateDocumentEdits(idbDatabase);
    console.log(`Migrated ${migratedEdits} document edits`);
    
    return { 
      success: migratedDocs > 0 || migratedVersions > 0 || migratedComments > 0 || migratedEdits > 0,
      message: `Migration completed successfully: ${migratedDocs} documents, ${migratedVersions} versions, ${migratedComments} comments, ${migratedEdits} edits`
    };
  } catch (error: any) {
    console.error('Migration failed:', error);
    return { 
      success: false, 
      message: `Migration failed: ${error.message}`
    };
  }
}

/**
 * Get a reference to the IndexedDB database
 * We need to use our own implementation since openDatabase is not exported
 */
async function getIndexedDBConnection(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    // Check if IndexedDB is supported
    if (!window.indexedDB) {
      reject(new Error('Your browser does not support IndexedDB'));
      return;
    }

    const request = window.indexedDB.open('BidManagementDB', 1);

    request.onerror = (event) => {
      reject(new Error('Error opening IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };
  });
}

/**
 * Debug function to inspect the structure of IndexedDB
 */
async function debugIndexedDBStructure(idbDatabase: IDBDatabase): Promise<void> {
  console.log('Analyzing IndexedDB structure...');
  
  const storeNames = Array.from(idbDatabase.objectStoreNames);
  console.log('Available stores:', storeNames);
  
  // For each store, get its structure and a sample item
  for (const storeName of storeNames) {
    try {
      console.log(`Analyzing store: ${storeName}`);
      
      // Get schema info
      const transaction = idbDatabase.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      // Log indexes
      const indexNames = Array.from(store.indexNames);
      console.log(`- Indexes: ${indexNames.join(', ')}`);
      
      // Get a sample item
      const request = store.openCursor();
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          console.log(`- Sample item from ${storeName}:`, cursor.value);
        } else {
          console.log(`- No items found in ${storeName}`);
        }
      };
      
      request.onerror = (event) => {
        console.error(`- Error accessing ${storeName}:`, (event.target as IDBRequest).error);
      };
      
      // Get count
      const countRequest = store.count();
      countRequest.onsuccess = () => {
        console.log(`- ${storeName} has ${countRequest.result} items`);
      };
    } catch (error) {
      console.error(`Error analyzing store ${storeName}:`, error);
    }
  }
}

/**
 * Migrate documents from IndexedDB to SQLite
 */
async function migrateDocuments(idbDatabase: IDBDatabase): Promise<number> {
  return new Promise((resolve, reject) => {
    const transaction = idbDatabase.transaction([DOCUMENT_STORE], 'readonly');
    const store = transaction.objectStore(DOCUMENT_STORE);
    const request = store.getAll();
    
    let count = 0;
    let errors = 0;

    request.onsuccess = async (event) => {
      const documents = (event.target as IDBRequest).result;
      console.log(`Found ${documents.length} documents in IndexedDB:`, documents);
      
      if (documents.length === 0) {
        console.warn('No documents found in IndexedDB to migrate');
        resolve(0);
        return;
      }
      
      for (let i = 0; i < documents.length; i++) {
        const idbDoc = documents[i];
        try {
          // Basic validation
          if (!idbDoc.id) {
            console.error(`Document at index ${i} missing ID, skipping:`, idbDoc);
            errors++;
            continue;
          }
          
          console.log(`Migrating document ${i+1}/${documents.length}: ${idbDoc.id}`, idbDoc);
          
          // Create a safe document object with fallbacks for all required fields
          const sqliteDoc: any = {
            id: idbDoc.id,
            bidId: idbDoc.bidId || '',
            title: idbDoc.title || `Document ${idbDoc.id}`,
            content: idbDoc.content || '',
            createdAt: idbDoc.createdAt || new Date().toISOString(),
            updatedAt: idbDoc.updatedAt || new Date().toISOString(),
            createdBy: idbDoc.createdBy || '',
            status: (idbDoc.status || 'draft') as any,
            lastVersionId: idbDoc.lastVersionId || null,
            lastVersion: idbDoc.lastVersion || null,
            metadata: {}
          };
          
          // Copy any additional properties to metadata
          for (const key in idbDoc) {
            if (!sqliteDoc.hasOwnProperty(key) && key !== 'metadata') {
              sqliteDoc.metadata[key] = idbDoc[key];
            }
          }
          
          // If the document already has metadata, merge it
          if (idbDoc.metadata && typeof idbDoc.metadata === 'object') {
            sqliteDoc.metadata = {
              ...sqliteDoc.metadata,
              ...idbDoc.metadata
            };
          }
          
          console.log(`Saving document ${idbDoc.id} to SQLite:`, sqliteDoc);
          await sqlitedb.saveDocument(sqliteDoc);
          console.log(`Document ${idbDoc.id} migrated successfully`);
          count++;
        } catch (error) {
          console.error(`Error migrating document ${idbDoc?.id || 'unknown'} (index ${i}):`, error);
          errors++;
        }
      }
      
      console.log(`Documents migration complete: ${count} migrated, ${errors} errors`);
      resolve(count);
    };
    
    request.onerror = (event) => {
      const errorMsg = `Failed to retrieve documents: ${(event.target as IDBRequest).error}`;
      console.error(errorMsg);
      reject(new Error(errorMsg));
    };
  });
}

/**
 * Migrate document versions from IndexedDB to SQLite
 */
async function migrateDocumentVersions(idbDatabase: IDBDatabase): Promise<number> {
  return new Promise((resolve, reject) => {
    const transaction = idbDatabase.transaction([VERSION_STORE], 'readonly');
    const store = transaction.objectStore(VERSION_STORE);
    const request = store.getAll();
    
    let count = 0;

    request.onsuccess = async (event) => {
      const versions = (event.target as IDBRequest).result;
      
      for (const idbVersion of versions) {
        try {
          // Check if the parent document exists first
          const documentExists = await checkDocumentExists(idbVersion.documentId);
          
          if (!documentExists) {
            console.warn(`Document ${idbVersion.documentId} doesn't exist. Creating placeholder document before saving version.`);
            
            // Create a placeholder document if needed
            const placeholderDoc: any = {
              id: idbVersion.documentId,
              bidId: '',  // This might need to be set to a valid bid ID
              title: 'Document ' + idbVersion.documentId,
              content: '',
              createdAt: idbVersion.metadata?.createdAt || new Date().toISOString(),
              updatedAt: idbVersion.metadata?.createdAt || new Date().toISOString(),
              createdBy: idbVersion.metadata?.createdBy?.id || '',
              status: 'draft' as any,
              lastVersionId: idbVersion.id, // Use current version's ID
              lastVersion: idbVersion.version || '1',
              metadata: {}
            };
            
            await sqlitedb.saveDocument(placeholderDoc);
          }
          
          // Map IndexedDB version to SQLite version structure
          const sqliteVersion: DocumentVersion = {
            id: idbVersion.id,
            documentId: idbVersion.documentId,
            content: idbVersion.content || '',
            version: idbVersion.version || '1',
            previousVersionId: idbVersion.previousVersionId || null,
            diff: idbVersion.diff || undefined,
            metadata: {
              status: idbVersion.metadata?.status || 'draft',
              createdAt: idbVersion.metadata?.createdAt || new Date().toISOString(),
              createdBy: {
                id: idbVersion.metadata?.createdBy?.id || '',
                name: idbVersion.metadata?.createdBy?.name || '',
                role: idbVersion.metadata?.createdBy?.role || ''
              },
              commitMessage: idbVersion.metadata?.commitMessage || ''
            }
          };
          
          await sqlitedb.saveDocumentVersion(sqliteVersion);
          count++;
        } catch (error) {
          console.error(`Error migrating document version ${idbVersion.id}:`, error);
        }
      }
      
      resolve(count);
    };
    
    request.onerror = (event) => {
      reject(new Error(`Failed to retrieve document versions: ${(event.target as IDBRequest).error}`));
    };
  });
}

/**
 * Check if a document exists in SQLite
 */
async function checkDocumentExists(documentId: string): Promise<boolean> {
  try {
    const doc = await sqlitedb.getDocument(documentId);
    return !!doc;
  } catch (error) {
    console.error(`Error checking if document ${documentId} exists:`, error);
    return false;
  }
}

/**
 * Migrate document comments from IndexedDB to SQLite
 */
async function migrateDocumentComments(idbDatabase: IDBDatabase): Promise<number> {
  return new Promise((resolve, reject) => {
    const transaction = idbDatabase.transaction([COMMENT_STORE], 'readonly');
    const store = transaction.objectStore(COMMENT_STORE);
    const request = store.getAll();
    
    let count = 0;

    request.onsuccess = async (event) => {
      const comments = (event.target as IDBRequest).result;
      
      // First, organize comments into a parent-child structure
      const parentComments: DocumentComment[] = [];
      const childComments: { [parentId: string]: DocumentComment[] } = {};
      
      for (const idbComment of comments) {
        try {
          // Map IndexedDB comment to SQLite comment structure
          const sqliteComment: DocumentComment = {
            id: idbComment.id,
            documentId: idbComment.documentId,
            versionId: idbComment.versionId,
            content: idbComment.content || '',
            author: {
              id: idbComment.author?.id || '',
              name: idbComment.author?.name || '',
              avatar: idbComment.author?.avatar
            },
            createdAt: idbComment.createdAt || new Date().toISOString(),
            selection: idbComment.selection,
            resolved: idbComment.resolved || false,
            replies: []
          };
          
          // Organize into parent/child structure
          if (idbComment.parent_comment_id) {
            if (!childComments[idbComment.parent_comment_id]) {
              childComments[idbComment.parent_comment_id] = [];
            }
            childComments[idbComment.parent_comment_id].push(sqliteComment);
          } else {
            parentComments.push(sqliteComment);
          }
        } catch (error) {
          console.error(`Error processing document comment ${idbComment.id}:`, error);
        }
      }
      
      // Assign child comments to their parents
      parentComments.forEach(parent => {
        if (childComments[parent.id]) {
          parent.replies = childComments[parent.id];
        }
      });
      
      // Save all parent comments (which will also save their replies)
      for (const comment of parentComments) {
        try {
          await sqlitedb.saveDocumentComment(comment);
          count++; // Count the parent
          count += comment.replies?.length || 0; // Count all replies, with proper null check
        } catch (error) {
          console.error(`Error migrating document comment ${comment.id}:`, error);
        }
      }
      
      resolve(count);
    };
    
    request.onerror = (event) => {
      reject(new Error(`Failed to retrieve document comments: ${(event.target as IDBRequest).error}`));
    };
  });
}

/**
 * Migrate document edits from IndexedDB to SQLite
 */
async function migrateDocumentEdits(idbDatabase: IDBDatabase): Promise<number> {
  return new Promise((resolve, reject) => {
    const transaction = idbDatabase.transaction([EDIT_STORE], 'readonly');
    const store = transaction.objectStore(EDIT_STORE);
    const request = store.getAll();
    
    let count = 0;

    request.onsuccess = async (event) => {
      const edits = (event.target as IDBRequest).result;
      
      for (const idbEdit of edits) {
        try {
          // Map IndexedDB edit to SQLite edit structure
          const sqliteEdit = {
            id: idbEdit.id,
            documentId: idbEdit.documentId,
            content: idbEdit.content || '',
            timestamp: idbEdit.timestamp || new Date().toISOString()
          };
          
          await sqlitedb.saveDocumentEdit(sqliteEdit);
          count++;
        } catch (error) {
          console.error(`Error migrating document edit ${idbEdit.id}:`, error);
        }
      }
      
      resolve(count);
    };
    
    request.onerror = (event) => {
      reject(new Error(`Failed to retrieve document edits: ${(event.target as IDBRequest).error}`));
    };
  });
}

/**
 * Validate the migration by comparing record counts
 */
export async function validateMigration(): Promise<{ success: boolean, details: any }> {
  try {
    const idbDatabase = await getIndexedDBConnection();
    await sqlitedb.initSQLite();
    
    // Get counts from IndexedDB
    const idbCounts = await getIndexedDBCounts(idbDatabase);
    
    // Get counts from SQLite
    const sqliteCounts = await getSQLiteCounts();
    
    // Compare counts
    const success = 
      idbCounts.documents === sqliteCounts.documents &&
      idbCounts.versions === sqliteCounts.versions &&
      idbCounts.comments === sqliteCounts.comments &&
      idbCounts.edits === sqliteCounts.edits;
    
    return {
      success,
      details: {
        indexeddb: idbCounts,
        sqlite: sqliteCounts,
        matches: {
          documents: idbCounts.documents === sqliteCounts.documents,
          versions: idbCounts.versions === sqliteCounts.versions,
          comments: idbCounts.comments === sqliteCounts.comments,
          edits: idbCounts.edits === sqliteCounts.edits
        }
      }
    };
  } catch (error: any) {
    console.error('Validation failed:', error);
    return { 
      success: false, 
      details: { error: error.message }
    };
  }
}

/**
 * Get record counts from IndexedDB
 */
async function getIndexedDBCounts(idbDatabase: IDBDatabase): Promise<any> {
  try {
    console.log('Getting IndexedDB counts...');
    
    // Check if stores exist first
    const storeNames = Array.from(idbDatabase.objectStoreNames);
    console.log('Available IndexedDB stores:', storeNames);
    
    // Prepare default counts
    const counts = {
      documents: 0,
      versions: 0,
      comments: 0,
      edits: 0
    };
    
    // Only try to count if the stores exist
    if (storeNames.includes(DOCUMENT_STORE)) {
      counts.documents = await getStoreCount(idbDatabase, DOCUMENT_STORE);
    } else {
      console.warn(`Store '${DOCUMENT_STORE}' not found in IndexedDB`);
    }
    
    if (storeNames.includes(VERSION_STORE)) {
      counts.versions = await getStoreCount(idbDatabase, VERSION_STORE);
    } else {
      console.warn(`Store '${VERSION_STORE}' not found in IndexedDB`);
    }
    
    if (storeNames.includes(COMMENT_STORE)) {
      counts.comments = await getStoreCount(idbDatabase, COMMENT_STORE);
    } else {
      console.warn(`Store '${COMMENT_STORE}' not found in IndexedDB`);
    }
    
    if (storeNames.includes(EDIT_STORE)) {
      counts.edits = await getStoreCount(idbDatabase, EDIT_STORE);
    } else {
      console.warn(`Store '${EDIT_STORE}' not found in IndexedDB`);
    }
    
    console.log('IndexedDB counts:', counts);
    return counts;
  } catch (error) {
    console.error('Error getting IndexedDB counts:', error);
    return {
      documents: 0,
      versions: 0,
      comments: 0,
      edits: 0
    };
  }
}

/**
 * Get record counts from SQLite
 */
async function getSQLiteCounts(): Promise<any> {
  try {
    // Use proper aggregate functions
    const countDocumentsResult = sqlitedb.executeSQL('SELECT COUNT(*) as count FROM documents');
    const countVersionsResult = sqlitedb.executeSQL('SELECT COUNT(*) as count FROM document_versions');
    const countCommentsResult = sqlitedb.executeSQL('SELECT COUNT(*) as count FROM document_comments');
    const countEditsResult = sqlitedb.executeSQL('SELECT COUNT(*) as count FROM document_edits');
    
    console.log('SQLite counts raw results:', {
      docs: countDocumentsResult,
      versions: countVersionsResult,
      comments: countCommentsResult,
      edits: countEditsResult
    });
    
    // Parse results properly
    const documentsCount = parseInt(countDocumentsResult?.count?.toString() || '0', 10);
    const versionsCount = parseInt(countVersionsResult?.count?.toString() || '0', 10);
    const commentsCount = parseInt(countCommentsResult?.count?.toString() || '0', 10);
    const editsCount = parseInt(countEditsResult?.count?.toString() || '0', 10);
    
    return {
      documents: documentsCount, 
      versions: versionsCount,
      comments: commentsCount,
      edits: editsCount
    };
  } catch (error) {
    console.error('Error getting SQLite counts:', error);
    return {
      documents: 0,
      versions: 0,
      comments: 0,
      edits: 0
    };
  }
}

/**
 * Get count of items in a store
 */
async function getStoreCount(idbDatabase: IDBDatabase, storeName: string): Promise<number> {
  return new Promise<number>((resolve, reject) => {
    try {
      const transaction = idbDatabase.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.count();
      
      request.onsuccess = (event) => {
        resolve((event.target as IDBRequest<number>).result);
      };
      
      request.onerror = (event) => {
        reject(new Error(`Failed to count items in ${storeName}: ${(event.target as IDBRequest).error}`));
      };
    } catch (error) {
      reject(error);
    }
  });
}

const migration = {
  migrateToSQLite,
  validateMigration
};

export default migration; 