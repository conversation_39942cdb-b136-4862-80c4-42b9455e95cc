import { User } from '@/types';

/**
 * Mock user data for UI display purposes
 * This is used for displaying user information in comments and other UI elements
 * In a production environment, this would be replaced with real user data from an API
 */
export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'ADMIN',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'MANAGER',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=32&h=32&fit=crop&crop=face',
  },
];