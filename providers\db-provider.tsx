"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as IndexedDB from '@/lib/indexeddb';
import * as SQLiteDB from '@/lib/sqlitedb';

type DBProvider = 'indexeddb' | 'sqlite';

interface DBContextValue {
  provider: DBProvider;
  setProvider: (provider: DBProvider) => void;
  isAvailable: boolean;
  
  // Shared database methods 
  saveDocument: (document: any) => Promise<void>;
  getDocument: (documentId: string) => Promise<any>;
  getDocumentsByBid: (bidId: string) => Promise<any[]>;
  saveDocumentVersion: (version: any) => Promise<void>;
  getDocumentVersion: (versionId: string) => Promise<any>;
  getDocumentVersions: (documentId: string, options?: any) => Promise<any[]>;
  getLatestVersion: (documentId: string) => Promise<any>;
  saveDocumentComment: (comment: any) => Promise<void>;
  getDocumentComments: (documentId: string, options?: any) => Promise<any[]>;
  saveDocumentEdit: (edit: any) => Promise<void>;
  getDocumentEdits: (documentId: string) => Promise<any[]>;
  clearDocumentEdits: (documentId: string) => Promise<void>;
  isDBAvailable: () => boolean;
  setupOfflineSync: () => void;
  initDatabase?: () => Promise<void>;
}

const DBContext = createContext<DBContextValue | null>(null);

export function DBProvider({ 
  children, 
  defaultProvider = 'sqlite',
}: { 
  children: ReactNode;
  defaultProvider?: DBProvider;
}) {
  const [provider, setProvider] = useState<DBProvider>(defaultProvider);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAvailable, setIsAvailable] = useState(false);

  useEffect(() => {
    // Initialize the database when the provider changes
    const initDB = async () => {
      try {
        if (provider === 'sqlite') {
          // Initialize SQLite database
          const available = SQLiteDB.isSQLiteAvailable();
          if (available) {
            await SQLiteDB.initSQLite();
            setIsAvailable(true);
          } else {
            // Try to initialize SQLite first
            try {
              await SQLiteDB.initSQLite();
              const sqliteAvailable = SQLiteDB.isSQLiteAvailable();
              if (sqliteAvailable) {
                setIsAvailable(true);
                return;
              }
            } catch (err) {
              console.warn('Could not initialize SQLite, falling back to IndexedDB', err);
            }
            
            // Fall back to IndexedDB if SQLite is not available
            console.log('SQLite not available, falling back to IndexedDB');
            setProvider('indexeddb');
            return;
          }
        } else {
          // Check IndexedDB availability
          const available = IndexedDB.isIndexedDBAvailable();
          setIsAvailable(available);
          
          // If IndexedDB is not available, try to switch back to SQLite
          if (!available) {
            try {
              await SQLiteDB.initSQLite();
              const sqliteAvailable = SQLiteDB.isSQLiteAvailable();
              if (sqliteAvailable) {
                console.log('IndexedDB not available, switching to SQLite');
                setProvider('sqlite');
                setIsAvailable(true);
                return;
              }
            } catch (err) {
              console.error('Neither IndexedDB nor SQLite are available', err);
              setIsAvailable(false);
            }
          }
        }
        setIsInitialized(true);
      } catch (error) {
        console.error(`Error initializing ${provider} database:`, error);
        setIsAvailable(false);
      }
    };

    initDB();
  }, [provider]);

  // Choose the implementation based on the selected provider
  const db = provider === 'sqlite' ? SQLiteDB : IndexedDB;

  // Create the context value with the selected database implementation
  const contextValue: DBContextValue = {
    provider,
    setProvider,
    isAvailable,
    // Forward all methods to the selected database implementation
    saveDocument: db.saveDocument,
    getDocument: db.getDocument,
    getDocumentsByBid: db.getDocumentsByBid,
    saveDocumentVersion: db.saveDocumentVersion,
    getDocumentVersion: db.getDocumentVersion,
    getDocumentVersions: db.getDocumentVersions,
    getLatestVersion: db.getLatestVersion,
    saveDocumentComment: db.saveDocumentComment,
    getDocumentComments: db.getDocumentComments,
    saveDocumentEdit: db.saveDocumentEdit,
    getDocumentEdits: db.getDocumentEdits,
    clearDocumentEdits: db.clearDocumentEdits,
    isDBAvailable: provider === 'sqlite' 
      ? () => (SQLiteDB as any).isSQLiteAvailable() 
      : () => (IndexedDB as any).isIndexedDBAvailable(),
    setupOfflineSync: db.setupOfflineSync,
    initDatabase: provider === 'sqlite' 
      ? () => (SQLiteDB as any).initSQLite() 
      : undefined,
  };

  return (
    <DBContext.Provider value={contextValue}>
      {children}
    </DBContext.Provider>
  );
}

export function useDB() {
  const context = useContext(DBContext);
  if (!context) {
    throw new Error('useDB must be used within a DBProvider');
  }
  return context;
}

// Custom hook that combines the DB context with document-specific functionality
export function useDocumentDB(documentId?: string, bidId?: string) {
  const db = useDB();
  const [isLoading, setIsLoading] = useState(false);
  const [document, setDocument] = useState<any | null>(null);
  const [versions, setVersions] = useState<any[]>([]);
  const [comments, setComments] = useState<any[]>([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Setup online/offline detection
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Load document data when documentId changes
  useEffect(() => {
    if (documentId && db.isAvailable) {
      // Load document
      loadDocument(documentId);
      loadVersions(documentId);
      loadComments(documentId);
    }
  }, [documentId, db.isAvailable]);

  // Load document
  const loadDocument = async (docId: string) => {
    if (!db.isAvailable) return;
    
    setIsLoading(true);
    try {
      const doc = await db.getDocument(docId);
      
      // If bidId is provided, verify the document belongs to the bid
      if (doc && bidId) {
        if (doc.bidId && doc.bidId !== bidId) {
          console.warn(`Document ${docId} belongs to bid ${doc.bidId}, not ${bidId}`);
          setDocument(null);
          setIsLoading(false);
          return;
        }
        
        // If document doesn't have a bidId, update it
        if (!doc.bidId) {
          const updatedDoc = { ...doc, bidId };
          await db.saveDocument(updatedDoc);
          setDocument(updatedDoc);
        } else {
          setDocument(doc);
        }
      } else {
        setDocument(doc);
      }
    } catch (error) {
      console.error("Error loading document:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load versions
  const loadVersions = async (docId: string) => {
    if (!db.isAvailable) return;
    
    setIsLoading(true);
    try {
      const docVersions = await db.getDocumentVersions(docId);
      setVersions(docVersions);
    } catch (error) {
      console.error("Error loading document versions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load comments
  const loadComments = async (docId: string) => {
    if (!db.isAvailable) return;
    
    setIsLoading(true);
    try {
      const docComments = await db.getDocumentComments(docId);
      setComments(docComments);
    } catch (error) {
      console.error("Error loading document comments:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Save document
  const saveDocument = async (doc: any) => {
    if (!db.isAvailable) return null;
    
    setIsLoading(true);
    try {
      // Make sure bidId is set if available
      if (bidId && !doc.bidId) {
        doc.bidId = bidId;
      }
      
      await db.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document:", error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Save document content
  const saveDocumentContent = async (docId: string, content: string) => {
    if (!db.isAvailable || !docId) return null;
    
    setIsLoading(true);
    try {
      // Get existing document or create a new one
      let doc = await db.getDocument(docId);
      
      if (!doc) {
        doc = {
          id: docId,
          bidId: bidId, // Set bidId for new documents
          content,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
      } else {
        doc.content = content;
        doc.updatedAt = new Date().toISOString();
        
        // Update bidId if needed
        if (bidId && !doc.bidId) {
          doc.bidId = bidId;
        }
      }
      
      // Save unsaved edit if offline
      if (!isOnline) {
        await db.saveDocumentEdit({
          id: crypto.randomUUID(),
          documentId: docId,
          content,
          timestamp: new Date().toISOString()
        });
      }
      
      await db.saveDocument(doc);
      setDocument(doc);
      return doc;
    } catch (error) {
      console.error("Error saving document content:", error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Create a new document version
  const createVersion = async (params: {
    documentId: string;
    content: string;
    commitMessage: string;
    userId: string;
    userName: string;
    userRole?: string;
  }) => {
    if (!db.isAvailable) return null;
    
    setIsLoading(true);
    try {
      // Get latest version to use as previous version
      const previousVersion = await db.getLatestVersion(params.documentId);
      
      // Create version control service instance
      const { DocumentVersionControl } = await import('@/lib/documents');
      const versionControl = new DocumentVersionControl();
      
      // Create the new version
      const newVersion = await versionControl.createVersion({
        documentId: params.documentId,
        content: params.content,
        previousVersion: previousVersion || undefined,
        commitMessage: params.commitMessage,
        userId: params.userId,
        userName: params.userName,
        userRole: params.userRole
      });
      
      // Save to database
      await db.saveDocumentVersion(newVersion);
      
      // Refresh versions list
      await loadVersions(params.documentId);
      
      return newVersion;
    } catch (error) {
      console.error("Error creating version:", error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    dbProvider: db.provider,
    setDBProvider: db.setProvider,
    isDBAvailable: db.isAvailable,
    isOnline,
    isLoading,
    document,
    versions,
    comments,
    loadDocument,
    loadVersions,
    loadComments,
    saveDocument,
    saveDocumentContent,
    createVersion,
  };
} 