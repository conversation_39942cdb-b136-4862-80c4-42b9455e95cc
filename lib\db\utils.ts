"use client";

import * as IndexedDB from '../indexeddb';
import sqlitedb from '../sqlitedb';

/**
 * Checks if the user should migrate data from IndexedDB to SQLite
 * Returns true if IndexedDB has data and SQLite is available
 */
export const shouldMigrateToSQLite = async (): Promise<boolean> => {
  try {
    // Check if SQLite is available
    if (!sqlitedb.isSQLiteAvailable()) {
      await sqlitedb.initSQLite();
    }
    
    if (!sqlitedb.isSQLiteAvailable()) {
      return false;
    }
    
    // Check if IndexedDB is available and has data
    if (!IndexedDB.isIndexedDBAvailable()) {
      return false;
    }
    
    // Get a sample document from IndexedDB to check if there's data
    const documents = await IndexedDB.getDocumentsByBid('any');
    const hasIndexedDBData = documents && documents.length > 0;
    
    // Already has data in SQLite?
    const hasSQLiteData = await sqlitedb.hasSQLiteData();
    
    // We should migrate if IndexedDB has data and either:
    // 1. SQLite has no data (fresh migration needed)
    // 2. SQLite has data but might be missing some from IndexedDB (partial migration)
    return hasIndexedDBData && (!hasSQLiteData || documents.length > 0);
  } catch (error) {
    console.error('Error checking migration status:', error);
    return false;
  }
};

/**
 * Checks if IndexedDB should be cleared (if SQLite has all the data)
 */
export const shouldClearIndexedDB = async (): Promise<boolean> => {
  try {
    // Check if SQLite is available and has data
    if (!sqlitedb.isSQLiteAvailable()) {
      await sqlitedb.initSQLite();
    }
    
    if (!sqlitedb.isSQLiteAvailable()) {
      return false;
    }
    
    const hasSQLiteData = await sqlitedb.hasSQLiteData();
    
    // Only suggest clearing IndexedDB if SQLite has data
    return hasSQLiteData;
  } catch (error) {
    console.error('Error checking if IndexedDB should be cleared:', error);
    return false;
  }
}; 