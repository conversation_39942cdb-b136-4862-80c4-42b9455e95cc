import { NextResponse } from "next/server";
import { getLatestVersion, saveDocumentVersion, getDocumentVersion } from "@/lib/documents";
import { DocumentVersionControl } from "@/lib/documents";

const versionControl = new DocumentVersionControl();

export async function PUT(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  try {
    const { content, userId, userName, userRole } = await req.json();
    
    if (!content || !userId || !userName) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Get the latest version to compare changes
    const latestVersion = await getLatestVersion(params.documentId);
    
    // Create a new version with proper diff tracking
    const newVersion = await versionControl.createVersion({
      documentId: params.documentId,
      content,
      previousVersion: latestVersion,
      commitMessage: "Document content updated",
      userId,
      userName,
      userRole
    });
    
    // Save the new version
    await saveDocumentVersion(newVersion);
    
    return NextResponse.json({ 
      success: true,
      version: newVersion.version
    });
  } catch (error) {
    console.error("Error updating document content:", error);
    return NextResponse.json(
      { error: "Failed to update document content" },
      { status: 500 }
    );
  }
}

export async function GET(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  try {
    const searchParams = new URL(req.url).searchParams;
    const versionId = searchParams.get("versionId");
    
    let content = "";
    
    if (versionId) {
      // If a specific version is requested, get that version
      const version = await getDocumentVersion(versionId);
      if (version) {
        content = version.content;
      }
    } else {
      // Otherwise get the latest version
      const latestVersion = await getLatestVersion(params.documentId);
      
      if (latestVersion) {
        content = latestVersion.content;
      }
    }
    
    return NextResponse.json({ content });
  } catch (error) {
    console.error("Error fetching document content:", error);
    return NextResponse.json(
      { error: "Failed to fetch document content" },
      { status: 500 }
    );
  }
}
