{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "registry:build": "npx shadcn@canary build"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tiptap/extension-collaboration": "^2.11.5", "@tiptap/extension-collaboration-cursor": "^2.11.5", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/axios": "^0.14.4", "@types/d3": "^7.4.3", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@typescript-eslint/eslint-plugin": "^8.28.0", "@xmldom/xmldom": "^0.9.8", "autoprefixer": "10.4.15", "axios": "^1.8.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "diff-match-patch": "^1.0.5", "embla-carousel-react": "^8.3.0", "eslint": "^9.23.0", "eslint-config-next": "^15.2.4", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.0", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.446.0", "next": "^14.2.24", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "postcss": "^8.5.3", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "shadcn-ui-mcp-server": "^0.1.2", "sonner": "^1.5.0", "sql.js": "^1.9.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^0.9.9", "ws": "^8.18.1", "y-indexeddb": "^9.0.12", "y-protocols": "^1.0.6", "y-websocket": "^2.1.0", "yjs": "^13.6.24", "zod": "^3.23.8"}, "devDependencies": {"@types/diff-match-patch": "^1.0.36", "@types/jest": "^29.5.12", "@types/sql.js": "^1.4.9", "@types/xmldom": "^0.1.34", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "shadcn": "^2.6.0-canary.2", "ts-jest": "^29.1.2"}}