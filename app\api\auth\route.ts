export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithOTDS, validateAppWorksSession } from '@/lib/auth';
import { cookies } from 'next/headers';

/**
 * Handles POST requests for user authentication.
 *
 * This function processes a POST request to authenticate a user using OTDS (OpenText Directory Services).
 * It expects a JSON body with 'userName' and 'password' fields. If the credentials are valid, it further
 * validates the AppWorks session associated with the authentication ticket. Upon successful validation,
 * it sets an authentication cookie and returns a success response.
 *
 * @param {NextRequest} request - The incoming request object containing user credentials.
 * @returns {Promise<NextResponse>} - A response indicating the result of the authentication process.
 *
 * Possible response statuses:
 * - 200: Authentication and session validation are successful.
 * - 400: Missing user credentials.
 * - 401: Authentication or session validation failed.
 * - 500: An unexpected server error occurred.
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userName, password } = body;

    if (!userName || !password) {
      return NextResponse.json(
        { 
          status: 'error',
          code: 'INVALID_CREDENTIALS',
          message: 'Username and password are required' 
        },
        { status: 400 }
      );
    }

    try {
      // Authenticate with OTDS
      const otdsResponse = await authenticateWithOTDS({ userName, password });

      // If we get here, authentication was successful
      // Now validate AppWorks session
      const isValidAppWorksSession = await validateAppWorksSession(otdsResponse.ticket);

      if (!isValidAppWorksSession) {
        return NextResponse.json(
          { 
            status: 'error',
            code: 'INVALID_SESSION',
            message: 'Failed to validate AppWorks session' 
          },
          { status: 401 }
        );
      }

      // Set auth cookie
      cookies().set('user', JSON.stringify(otdsResponse), {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24, // 24 hours
        path: '/',
      });

      return NextResponse.json({
        status: 'success',
        data: otdsResponse
      });

    } catch (error: any) {
      // Handle OTDS authentication errors
      if (error.name === 'AuthenticationError') {
        return NextResponse.json(
          { 
            status: 'error',
            code: 'AUTH_FAILED',
            message: error.message
          },
          { status: 401 }
        );
      }
      throw error; // Re-throw other errors to be caught by outer catch
    }
  } catch (error: any) {
    console.error('Server error:', error);
    return NextResponse.json(
      { 
        status: 'error',
        code: 'SERVER_ERROR',
        message: 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
}

/**
 * Handles DELETE requests to log out a user.
 *
 * This function deletes the authentication cookie and returns a success response.
 *
 * @returns {Promise<NextResponse>} - A response indicating the result of the logout process.
 *
 * Possible response statuses:
 * - 200: Logout is successful.
 * - 500: An unexpected server error occurred.
 */
export async function DELETE() {
  cookies().delete('user');
  return NextResponse.json({ success: true });
}
