import { Bid, Status } from "@/types";
import axios from "axios";
import { format } from "date-fns";

// Type definition for the Appworks API Bid format
interface AppworksBidRequest {
  Properties: {
    Bid_Title: string;
    Bid_Description: string;
    Bid_Budget: number;
    Bid_Status: string;
    Bid_OpportunitySource: string;
    Bid_BusinessNeed: string;
    Bid_StrategicAlignment: string;
    Bid_PotentialBenefits: string;
    Bid_PreliminaryScope: string;
    Bid_Stakeholders: string;
    Bid_RiskAssessment: string;
    Bid_BidStrategy: string;
    Bid_CompetitiveAnalysis: string;
    Bid_WinStrategy: string;
    Bid_TechnicalApproach: string;
    Bid_PricingStrategy: string;
    Bid_QualityStandards: string;
    Bid_ComplianceRequirements: string;
    [key: string]: string | number;
  };
}

// Type definition for Appworks API response
interface AppworksBidResponse {
  Identity: {
    Id: string;
  };
  _links: {
    self: {
      href: string;
    };
  };
}

// Type definition for the New Bid form data
export interface NewBidData {
  title: string;
  description: string;
  budget: number;
  dueDate: Date;
  opportunitySource: string;
  businessNeed: string;
  strategicAlignment: string;
  potentialBenefits: string;
  preliminaryScope: string;
  stakeholders: string;
  riskAssessment: string;
  bidStrategy: string;
  winStrategy: string;
  competitiveAnalysis: string;
  technicalApproach: string;
  pricingStrategy: string;
  teamComposition: string;
  resourceRequirements: string;
  deliverables: string;
  milestones: string;
  qualityStandards: string;
  complianceRequirements: string;
  [key: string]: string | number | Date;
}

/**
 * Maps the application's form data to the Appworks API request format
 */
function mapToAppworksFormat(bidData: NewBidData): AppworksBidRequest {
  return {
    Properties: {
      Bid_Title: bidData.title,
      Bid_Description: bidData.description,
      Bid_Budget: bidData.budget,
      Bid_Status: "DRAFT", // New bids default to DRAFT status
      Bid_OpportunitySource: bidData.opportunitySource,
      Bid_BusinessNeed: bidData.businessNeed,
      Bid_StrategicAlignment: bidData.strategicAlignment,
      Bid_PotentialBenefits: bidData.potentialBenefits,
      Bid_PreliminaryScope: bidData.preliminaryScope,
      Bid_Stakeholders: bidData.stakeholders,
      Bid_RiskAssessment: bidData.riskAssessment,
      Bid_BidStrategy: bidData.bidStrategy,
      Bid_CompetitiveAnalysis: bidData.competitiveAnalysis,
      Bid_WinStrategy: bidData.winStrategy,
      Bid_TechnicalApproach: bidData.technicalApproach,
      Bid_PricingStrategy: bidData.pricingStrategy,
      Bid_QualityStandards: bidData.qualityStandards,
      Bid_ComplianceRequirements: bidData.complianceRequirements,
      // Additional fields can be mapped as needed
      Bid_TeamComposition: bidData.teamComposition,
      Bid_ResourceRequirements: bidData.resourceRequirements,
      Bid_Deliverables: bidData.deliverables,
      Bid_Milestones: bidData.milestones,
      // Format date as yyyy-MM-dd without time component
      Bid_DueDate: format(bidData.dueDate, 'yyyy-MM-dd'),
    }
  };
}

/**
 * Maps an Appworks bid response to our application's Bid format
 */
function mapFromAppworksFormat(response: AppworksBidResponse, originalData: NewBidData): Bid {
  return {
    bidId: response.Identity.Id,
    title: originalData.title,
    description: originalData.description,
    budget: originalData.budget,
    status: "DRAFT" as Status,
    dueDate: originalData.dueDate.toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: "", // This would come from the auth context
    assignedTo: [],
    documents: [],
    comments: []
  };
}

/**
 * Creates a new bid by calling the Appworks API
 * @param bidData The form data for the new bid
 * @param samlToken The SAML token for authentication
 * @returns The created bid in our application's format
 */
export async function createBid(bidData: NewBidData, samlToken: string): Promise<Bid> {
  try {
    const appworksRequest = mapToAppworksFormat(bidData);
    
    // Call our proxy API endpoint to avoid CORS issues
    const response = await axios.post(
      '/api/proxy/appworks/entity?entityType=Bid', 
      appworksRequest, 
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${samlToken}`
        }
      }
    );
    
    console.log('Bid created successfully:', response.data);
    
    // Map the response to our application's format
    return mapFromAppworksFormat(response.data, bidData);
  } catch (error: any) {
    console.error('Error creating bid:', error);
    console.error('Response data:', error.response?.data);
    throw new Error(`Failed to create bid: ${error.message}`);
  }
}

/**
 * Gets a list of bids from Appworks
 * This is a placeholder for future implementation
 */
export async function getBids(samlToken: string): Promise<Bid[]> {
  // To be implemented
  return [];
}

/**
 * Gets a specific bid by ID from Appworks
 * This is a placeholder for future implementation
 */
export async function getBidById(bidId: string, samlToken: string): Promise<Bid | null> {
  // To be implemented
  return null;
}
