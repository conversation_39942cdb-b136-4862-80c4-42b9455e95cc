@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Modern Light Theme */
    --background: 210 40% 98%;
    --foreground: 222 47% 11%;
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;
    --primary: 221 83% 53%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215 16% 47%;
    --accent: 199 89% 48%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    --chart-1: 221 83% 53%;
    --chart-2: 199 89% 48%;
    --chart-3: 262 83% 58%;
    --chart-4: 340 82% 52%;
    --chart-5: 130 94% 38%;
    --radius: 0.5rem;
  }
  .dark {
    /* Modern Dark Theme - Teal, Grey, Gold */
    --background: 220 20% 12%; /* Darker grey */
    --foreground: 210 40% 98%; /* Light grey text */
    --card: 220 16% 16%; /* Slightly lighter grey for cards */
    --card-foreground: 210 40% 98%; /* Light grey text */
    --popover: 220 16% 16%; /* Match card background */
    --popover-foreground: 210 40% 98%; /* Light grey text */
    --primary: 175 70% 41%; /* Teal primary */
    --primary-foreground: 0 0% 100%; /* White text on teal */
    --secondary: 220 14% 22%; /* Medium grey */
    --secondary-foreground: 210 40% 98%; /* Light grey text */
    --muted: 220 10% 20%; /* Muted grey */
    --muted-foreground: 215 20% 70%; /* Lighter grey text for muted areas */
    --accent: 42 87% 55%; /* Gold accent */
    --accent-foreground: 220 20% 12%; /* Dark background on gold */
    --destructive: 0 62.8% 30.6%; /* Keep destructive red */
    --destructive-foreground: 210 40% 98%; /* Light text on destructive */
    --border: 220 16% 22%; /* Slightly lighter border */
    --input: 220 16% 22%; /* Match border */
    --ring: 175 70% 41%; /* Teal ring to match primary */
    --chart-1: 175 70% 41%; /* Teal */
    --chart-2: 42 87% 55%; /* Gold */
    --chart-3: 190 90% 50%; /* Light teal */
    --chart-4: 220 14% 65%; /* Light grey */
    --chart-5: 160 84% 39%; /* Green-teal */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-image: linear-gradient(to bottom, hsl(var(--background)), hsl(var(--background)));
  }
}

/* Enhanced UI Elements */
@layer components {
  .card,
  .popover,
  .dropdown-menu {
    @apply shadow-lg;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .dark .card,
  .dark .popover,
  .dark .dropdown-menu {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
    transition: all 0.2s ease;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80;
    transition: all 0.2s ease;
  }

  .btn-accent {
    @apply bg-accent text-accent-foreground hover:bg-accent/90;
    transition: all 0.2s ease;
  }

  /* Card animations */
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 15s ease infinite;
  }
}
