/**
 * Tests for SQLite database implementation
 * 
 * These tests check if the SQLite implementation correctly handles
 * document operations (CRUD) as expected.
 */

import * as SQLiteDB from '../lib/sqlitedb';
import { initSQLite, closeSQLite } from '../lib/sqlitedb';
import { DocumentVersion, DocumentComment, DocumentStatus } from '../types';
import { Document } from '../types/sqlitedb';

// Mock implementation for window.crypto.randomUUID
global.crypto = {
  ...global.crypto,
  randomUUID: (): `${string}-${string}-${string}-${string}-${string}` => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    }) as `${string}-${string}-${string}-${string}-${string}`;
  },
};

describe('SQLite Database', () => {
  // Initialize database before tests
  beforeAll(async () => {
    await initSQLite();
  });

  // Close database after tests
  afterAll(async () => {
    await closeSQLite();
  });

  // Clean up after each test
  afterEach(async () => {
    await SQLiteDB.executeSQL('DELETE FROM document_comments');
    await SQLiteDB.executeSQL('DELETE FROM document_versions');
    await SQLiteDB.executeSQL('DELETE FROM document_edits');
    await SQLiteDB.executeSQL('DELETE FROM documents');
  });

  describe('Document Operations', () => {
    test('should save and retrieve a document', async () => {
      // Create test document
      const testDoc: Document = {
        id: 'doc-test-1',
        bidId: 'bid-test-1',
        title: 'Test Document',
        content: 'This is test content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      // Save document
      await SQLiteDB.saveDocument(testDoc);

      // Retrieve document
      const retrievedDoc = await SQLiteDB.getDocument(testDoc.id);

      // Verify document
      expect(retrievedDoc).not.toBeNull();
      expect(retrievedDoc?.id).toBe(testDoc.id);
      expect(retrievedDoc?.bidId).toBe(testDoc.bidId);
      expect(retrievedDoc?.title).toBe(testDoc.title);
      expect(retrievedDoc?.content).toBe(testDoc.content);
    });

    test('should retrieve documents by bid ID', async () => {
      // Create test documents with same bid ID
      const bidId = 'bid-test-batch';
      const docs: Document[] = [
        {
          id: 'doc-test-bid-1',
          bidId,
          title: 'Doc 1',
          content: 'Content 1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'test-user',
          status: 'draft',
        },
        {
          id: 'doc-test-bid-2',
          bidId,
          title: 'Doc 2',
          content: 'Content 2',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'test-user',
          status: 'draft',
        },
      ];

      // Save documents
      await Promise.all(docs.map(doc => SQLiteDB.saveDocument(doc)));

      // Retrieve documents by bid ID
      const retrievedDocs = await SQLiteDB.getDocumentsByBid(bidId);

      // Verify retrieval
      expect(retrievedDocs.length).toBe(2);
      expect(retrievedDocs.map(d => d.id).sort()).toEqual(['doc-test-bid-1', 'doc-test-bid-2'].sort());
    });
  });

  describe('Document Versions', () => {
    test('should save and retrieve document versions', async () => {
      // Create test document
      const docId = 'doc-versions-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-test',
        title: 'Version Test Doc',
        content: 'Initial content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      // Save document
      await SQLiteDB.saveDocument(testDoc);

      // Create version
      const versionId = 'version-test-1';
      const version: DocumentVersion = {
        id: versionId,
        documentId: docId,
        content: 'Version 1 content',
        version: '1.0',
        previousVersionId: null,
        metadata: {
          status: 'draft',
          createdAt: new Date().toISOString(),
          createdBy: {
            id: 'user-1',
            name: 'Test User',
            role: 'Editor',
          },
          commitMessage: 'Initial version',
        },
      };

      // Save version
      await SQLiteDB.saveDocumentVersion(version);

      // Retrieve version
      const retrievedVersion = await SQLiteDB.getDocumentVersion(versionId);

      // Verify version
      expect(retrievedVersion).not.toBeNull();
      expect(retrievedVersion?.id).toBe(versionId);
      expect(retrievedVersion?.documentId).toBe(docId);
      expect(retrievedVersion?.content).toBe('Version 1 content');
      
      // Check database-specific fields from the mapped version
      if (retrievedVersion) {
        // @ts-ignore - Access the fields that exist in the SQLite version but not in the type
        expect(retrievedVersion.createdBy_id).toBe('user-1');
        // @ts-ignore
        expect(retrievedVersion.createdBy_name).toBe('Test User');
        // @ts-ignore
        expect(retrievedVersion.commitMessage).toBe('Initial version');
      }

      // Retrieve all versions
      const allVersions = await SQLiteDB.getDocumentVersions(docId);
      expect(allVersions.length).toBe(1);
    });

    test('should get latest version', async () => {
      // Create test document
      const docId = 'doc-latest-version-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-latest-test',
        title: 'Latest Version Test',
        content: 'Content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      await SQLiteDB.saveDocument(testDoc);

      // Create multiple versions with different timestamps
      const versions = [
        {
          id: 'v1',
          documentId: docId,
          content: 'V1 content',
          version: '1.0',
          previousVersionId: null,
          metadata: {
            status: 'draft',
            createdAt: new Date(Date.now() - 30000).toISOString(), // 30 seconds ago
            createdBy: { id: 'user-1', name: 'User', role: 'Editor' },
          },
        },
        {
          id: 'v2',
          documentId: docId,
          content: 'V2 content',
          version: '2.0',
          previousVersionId: 'v1',
          metadata: {
            status: 'draft',
            createdAt: new Date().toISOString(), // now (latest)
            createdBy: { id: 'user-1', name: 'User', role: 'Editor' },
          },
        },
      ];

      // Save versions
      await Promise.all(versions.map(v => SQLiteDB.saveDocumentVersion(v)));

      // Get latest version
      const latestVersion = await SQLiteDB.getLatestVersion(docId);

      // Verify it's the correct one
      expect(latestVersion).not.toBeNull();
      expect(latestVersion?.id).toBe('v2');
      expect(latestVersion?.version).toBe('2.0');
    });
  });

  describe('Document Comments', () => {
    test('should save and retrieve comments', async () => {
      // Create test document
      const docId = 'doc-comments-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-comments-test',
        title: 'Comments Test Doc',
        content: 'Content for comment testing',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      await SQLiteDB.saveDocument(testDoc);

      // Create comment
      const commentId = 'comment-test-1';
      const comment: DocumentComment = {
        id: commentId,
        documentId: docId,
        content: 'Test comment',
        author: {
          id: 'user-1',
          name: 'Commenter',
          avatar: 'avatar-url',
        },
        createdAt: new Date().toISOString(),
        resolved: false,
        replies: [], // No replies initially
      };

      // Save comment
      await SQLiteDB.saveDocumentComment(comment);

      // Retrieve all comments
      const comments = await SQLiteDB.getDocumentComments(docId);

      // Verify comment
      expect(comments.length).toBe(1);
      expect(comments[0].id).toBe(commentId);
      expect(comments[0].content).toBe('Test comment');
      
      // Check database-specific fields from the mapped comment
      // @ts-ignore - Access the fields that exist in the SQLite result but not in the type
      expect(comments[0].author_id).toBe('user-1');
      // @ts-ignore
      expect(comments[0].author_name).toBe('Commenter');
    });

    test('should handle comment replies', async () => {
      // Create test document
      const docId = 'doc-reply-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-reply-test',
        title: 'Reply Test Doc',
        content: 'Content for testing replies',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      await SQLiteDB.saveDocument(testDoc);

      // Create parent comment
      const parentId = 'parent-comment';
      const parentComment: DocumentComment = {
        id: parentId,
        documentId: docId,
        content: 'Parent comment',
        author: {
          id: 'user-1',
          name: 'Parent Author',
        },
        createdAt: new Date().toISOString(),
        resolved: false,
        replies: [],
      };

      // Save parent comment
      await SQLiteDB.saveDocumentComment(parentComment);

      // Create reply comment
      const replyId = 'reply-comment';
      const replyComment: DocumentComment & { parent_comment_id?: string } = {
        id: replyId,
        documentId: docId,
        content: 'Reply comment',
        author: {
          id: 'user-2',
          name: 'Reply Author',
        },
        createdAt: new Date().toISOString(),
        resolved: false,
        replies: [],
        // Use parent_comment_id in SQLite implementation
        parent_comment_id: parentId,
      };

      // Save reply
      await SQLiteDB.saveDocumentComment(replyComment);

      // Retrieve comments with hierarchical structure
      const comments = await SQLiteDB.getDocumentComments(docId);

      // Verify hierarchical structure is maintained
      expect(comments.length).toBe(2);
      
      // In SQLite implementation, we should get all comments with their relationships
      const parentComment1 = comments.find(c => c.id === parentId);
      const replyComment1 = comments.find(c => c.id === replyId);
      
      expect(parentComment1).not.toBeUndefined();
      expect(replyComment1).not.toBeUndefined();
      
      // @ts-ignore - Access the field that exists in the SQLite result but not in the type
      expect(replyComment1?.parent_comment_id).toBe(parentId);
    });
  });

  describe('Document Edits', () => {
    test('should save and retrieve document edits', async () => {
      // Create test document
      const docId = 'doc-edits-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-edits-test',
        title: 'Edits Test Doc',
        content: 'Initial content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      await SQLiteDB.saveDocument(testDoc);

      // Create edit
      const editId = 'edit-test-1';
      const edit = {
        id: editId,
        documentId: docId,
        content: 'Updated content during offline mode',
        timestamp: new Date().toISOString(),
      };

      // Save edit
      await SQLiteDB.saveDocumentEdit(edit);

      // Retrieve edits
      const edits = await SQLiteDB.getDocumentEdits(docId);

      // Verify edits
      expect(edits.length).toBe(1);
      expect(edits[0].id).toBe(editId);
      expect(edits[0].content).toBe('Updated content during offline mode');
    });

    test('should clear document edits', async () => {
      // Create test document
      const docId = 'doc-clear-edits-test';
      const testDoc: Document = {
        id: docId,
        bidId: 'bid-clear-edits-test',
        title: 'Clear Edits Test',
        content: 'Content',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'test-user',
        status: 'draft',
      };

      await SQLiteDB.saveDocument(testDoc);

      // Create multiple edits
      const edits = [
        {
          id: 'edit-1',
          documentId: docId,
          content: 'Edit 1',
          timestamp: new Date().toISOString(),
        },
        {
          id: 'edit-2',
          documentId: docId,
          content: 'Edit 2',
          timestamp: new Date().toISOString(),
        },
      ];

      // Save edits
      await Promise.all(edits.map(e => SQLiteDB.saveDocumentEdit(e)));

      // Verify edits are saved
      const savedEdits = await SQLiteDB.getDocumentEdits(docId);
      expect(savedEdits.length).toBe(2);

      // Clear edits
      await SQLiteDB.clearDocumentEdits(docId);

      // Verify edits are cleared
      const clearedEdits = await SQLiteDB.getDocumentEdits(docId);
      expect(clearedEdits.length).toBe(0);
    });
  });
}); 