export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { headers } from 'next/headers';

// Define allowed origins (keeping consistent with the auth proxy)
const allowedOrigins = [
  process.env.NEXT_PUBLIC_APP_URL,
  'http://localhost:3000',
  'http://dbsapp.dcxeim.local:81'
];

export async function OPTIONS() {
  const headersList = headers();
  const origin = headersList.get('origin') || '';

  // Check if the origin is allowed
  if (allowedOrigins.includes(origin)) {
    return new NextResponse(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, SOAPAction',
        'Access-Control-Max-Age': '86400',
      },
    });
  }
  return new NextResponse(null, { status: 204 });
}

/**
 * Proxies requests to the AppWorks entity API endpoint,
 * passing through the request body and headers with the SAML token.
 * 
 * This handles entity operations like creating/updating bids.
 */
export async function POST(request: NextRequest) {
  try {
    const headersList = headers();
    const origin = headersList.get('origin') || '';
    
    // Get the SAMLart token from the request headers
    const samlToken = headersList.get('Authorization')?.replace('Bearer ', '');
    
    if (!samlToken) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'Missing authentication token',
        },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await request.json();
    
    // Get the entity type from the URL
    const url = new URL(request.url);
    const entityType = url.searchParams.get('entityType') || 'Bid';
    
    // Construct the AppWorks entity endpoint URL
    const appWorksUrl = `${process.env.NEXT_PUBLIC_APPWORKS_ENTITY_BASE_URL}/DatacentrixBidManagement/entities/${entityType}`;
    
    console.log('Making request to AppWorks entity API:', {
      url: appWorksUrl,
      entityType,
      bodyPreview: JSON.stringify(body).substring(0, 200) + '...',
    });
    
    // Make the request to AppWorks
    const response = await axios.post(appWorksUrl, body, {
      headers: {
        'Content-Type': 'application/json',
        'SAMLart': samlToken
      },
      proxy: false,
    });
    
    console.log('AppWorks entity API response:', {
      status: response.status,
      statusText: response.statusText,
      dataPreview: JSON.stringify(response.data).substring(0, 200) + '...',
    });
    
    // Prepare response headers
    const responseHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (allowedOrigins.includes(origin)) {
      responseHeaders['Access-Control-Allow-Origin'] = origin;
    }
    
    return NextResponse.json(response.data, {
      status: response.status,
      headers: responseHeaders,
    });
  } catch (error: any) {
    console.error('AppWorks entity proxy error:', {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Failed to communicate with AppWorks entity service',
        details: error.message,
        entityResponse: error.response?.data
      },
      { status: error.response?.status || 500 }
    );
  }
}
