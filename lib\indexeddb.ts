/**
 * IndexedDB service for document storage and offline support
 * Provides a complete API for storing and retrieving documents, versions, and comments
 */

// Database configuration
const DB_NAME = 'BidManagementDB';
const DB_VERSION = 1;
const DOCUMENT_STORE = 'documents';
const VERSION_STORE = 'documentVersions';
const COMMENT_STORE = 'documentComments';
const EDIT_STORE = 'documentEdits';

interface IndexedDBStores {
  documents: IDBObjectStore;
  documentVersions: IDBObjectStore;
  documentComments: IDBObjectStore;
  documentEdits: IDBObjectStore;
}

// Open the database and set up stores
const openDatabase = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    // Check if IndexedDB is supported
    if (!window.indexedDB) {
      reject(new Error('Your browser does not support IndexedDB'));
      return;
    }

    const request = window.indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      reject(new Error('Error opening IndexedDB'));
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create document store (if it doesn't exist)
      if (!db.objectStoreNames.contains(DOCUMENT_STORE)) {
        const documentsStore = db.createObjectStore(DOCUMENT_STORE, { keyPath: 'id' });
        documentsStore.createIndex('bidId', 'bidId', { unique: false });
        documentsStore.createIndex('updatedAt', 'updatedAt', { unique: false });
      }
      
      // Create document versions store
      if (!db.objectStoreNames.contains(VERSION_STORE)) {
        const versionsStore = db.createObjectStore(VERSION_STORE, { keyPath: 'id' });
        versionsStore.createIndex('documentId', 'documentId', { unique: false });
        versionsStore.createIndex('version', 'version', { unique: false });
        versionsStore.createIndex('createdAt', 'metadata.createdAt', { unique: false });
      }
      
      // Create document comments store
      if (!db.objectStoreNames.contains(COMMENT_STORE)) {
        const commentsStore = db.createObjectStore(COMMENT_STORE, { keyPath: 'id' });
        commentsStore.createIndex('documentId', 'documentId', { unique: false });
        commentsStore.createIndex('versionId', 'versionId', { unique: false });
        commentsStore.createIndex('createdAt', 'createdAt', { unique: false });
      }

      // Create edits store for unsaved/offline edits
      if (!db.objectStoreNames.contains(EDIT_STORE)) {
        const editsStore = db.createObjectStore(EDIT_STORE, { keyPath: 'id' });
        editsStore.createIndex('documentId', 'documentId', { unique: false });
        editsStore.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
};

/**
 * Generic function to perform a database operation
 */
const performDatabaseOperation = async <T>(
  storeName: string,
  mode: IDBTransactionMode,
  operation: (store: IDBObjectStore) => IDBRequest<T>
): Promise<T> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, mode);
    const store = transaction.objectStore(storeName);
    
    const request = operation(store);
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(new Error('Error performing IndexedDB operation'));
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Document operations
 */

export const saveDocument = async (document: any): Promise<void> => {
  // Add updatedAt timestamp if not present
  if (!document.updatedAt) {
    document.updatedAt = new Date().toISOString();
  }
  
  await performDatabaseOperation(
    DOCUMENT_STORE,
    'readwrite',
    (store) => store.put(document)
  );
};

export const getDocument = async (documentId: string): Promise<any> => {
  return performDatabaseOperation(
    DOCUMENT_STORE,
    'readonly',
    (store) => store.get(documentId)
  );
};

export const getDocumentsByBid = async (bidId: string): Promise<any[]> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(DOCUMENT_STORE, 'readonly');
    const store = transaction.objectStore(DOCUMENT_STORE);
    const index = store.index('bidId');
    const request = index.getAll(bidId);
    
    request.onsuccess = () => {
      resolve(request.result);
    };
    
    request.onerror = () => {
      reject(new Error('Error getting documents by bid'));
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Document version operations
 */

export const saveDocumentVersion = async (version: any): Promise<void> => {
  await performDatabaseOperation(
    VERSION_STORE,
    'readwrite',
    (store) => store.put(version)
  );
  
  // Update the document's last version info
  const document = await getDocument(version.documentId);
  if (document) {
    document.updatedAt = new Date().toISOString();
    document.lastVersionId = version.id;
    document.lastVersion = version.version;
    
    await saveDocument(document);
  }
};

export const getDocumentVersion = async (versionId: string): Promise<any> => {
  return performDatabaseOperation(
    VERSION_STORE,
    'readonly',
    (store) => store.get(versionId)
  );
};

export const getDocumentVersions = async (
  documentId: string,
  options: { limit?: number; offset?: number } = {}
): Promise<any[]> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    try {
      // If documentId is null, undefined, or "new", return empty array
      if (!documentId || documentId === "new") {
        console.log("Invalid documentId for version history:", documentId);
        resolve([]);
        return;
      }
      
      const transaction = db.transaction(VERSION_STORE, 'readonly');
      const store = transaction.objectStore(VERSION_STORE);
      const index = store.index('documentId');
      
      // Use a cursor to get only exact matches for documentId
      const request = index.openCursor(IDBKeyRange.only(documentId));
      const versions: any[] = [];
      
      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest<IDBCursorWithValue>).result;
        
        if (cursor) {
          // Add the version to our results array
          versions.push(cursor.value);
          cursor.continue();
        } else {
          // We've collected all versions, now sort and paginate
          
          // Sort by creation time, newest first
          versions.sort((a: any, b: any) => {
            const dateA = new Date(b.metadata?.createdAt || b.createdAt || 0).getTime();
            const dateB = new Date(a.metadata?.createdAt || a.createdAt || 0).getTime();
            return dateA - dateB;
          });
          
          // Apply pagination if specified
          let result = versions;
          if (options.offset || options.limit) {
            const offset = options.offset || 0;
            const limit = options.limit || result.length;
            result = result.slice(offset, offset + limit);
          }
          
          // Log the versions we're returning for debugging
          console.log(`Found ${versions.length} versions for document ${documentId}`, versions);
          
          resolve(result);
        }
      };
      
      request.onerror = () => {
        console.error('Error retrieving document versions for', documentId);
        reject(new Error('Error getting document versions'));
      };
      
      transaction.oncomplete = () => {
        db.close();
      };
    } catch (error) {
      console.error('Exception in getDocumentVersions:', error);
      resolve([]);
      db.close();
    }
  });
};

export const getLatestVersion = async (documentId: string): Promise<any> => {
  const versions = await getDocumentVersions(documentId, { limit: 1 });
  return versions.length > 0 ? versions[0] : undefined;
};

/**
 * Document comment operations
 */

export const saveDocumentComment = async (comment: any): Promise<void> => {
  await performDatabaseOperation(
    COMMENT_STORE,
    'readwrite',
    (store) => store.put(comment)
  );
};

export const getDocumentComments = async (
  documentId: string,
  options: { limit?: number; offset?: number; versionId?: string } = {}
): Promise<any[]> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(COMMENT_STORE, 'readonly');
    const store = transaction.objectStore(COMMENT_STORE);
    
    let request;
    if (options.versionId) {
      const index = store.index('versionId');
      request = index.getAll(options.versionId);
    } else {
      const index = store.index('documentId');
      request = index.getAll(documentId);
    }
    
    request.onsuccess = () => {
      let result = request.result;
      
      // Sort by creation time, newest first
      result.sort((a: any, b: any) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
      
      // Apply pagination if specified
      if (options.offset || options.limit) {
        const offset = options.offset || 0;
        const limit = options.limit || result.length;
        result = result.slice(offset, offset + limit);
      }
      
      resolve(result);
    };
    
    request.onerror = () => {
      reject(new Error('Error getting document comments'));
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Offline edit operations for unsaved changes
 */

export const saveDocumentEdit = async (edit: any): Promise<void> => {
  // Ensure we have a timestamp
  if (!edit.timestamp) {
    edit.timestamp = new Date().toISOString();
  }
  
  await performDatabaseOperation(
    EDIT_STORE,
    'readwrite',
    (store) => store.put(edit)
  );
};

export const getDocumentEdits = async (documentId: string): Promise<any[]> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(EDIT_STORE, 'readonly');
    const store = transaction.objectStore(EDIT_STORE);
    const index = store.index('documentId');
    const request = index.getAll(documentId);
    
    request.onsuccess = () => {
      const result = request.result;
      
      // Sort by timestamp, oldest first to apply edits in sequence
      result.sort((a: any, b: any) => {
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      });
      
      resolve(result);
    };
    
    request.onerror = () => {
      reject(new Error('Error getting document edits'));
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

export const clearDocumentEdits = async (documentId: string): Promise<void> => {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(EDIT_STORE, 'readwrite');
    const store = transaction.objectStore(EDIT_STORE);
    const index = store.index('documentId');
    const request = index.getAll(documentId);
    
    request.onsuccess = () => {
      const edits = request.result;
      
      // Delete each edit
      const deletePromises = edits.map((edit) => {
        return performDatabaseOperation(
          EDIT_STORE,
          'readwrite',
          (store) => store.delete(edit.id)
        );
      });
      
      Promise.all(deletePromises)
        .then(() => resolve())
        .catch(() => reject(new Error('Error clearing document edits')));
    };
    
    request.onerror = () => {
      reject(new Error('Error getting document edits to clear'));
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Check database connectivity
 */
export const isIndexedDBAvailable = (): boolean => {
  return !!window.indexedDB;
};

/**
 * Create a hook to detect online/offline status
 */
export const setupOfflineSync = () => {
  // Listen for online/offline events
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Clean up event listeners
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};

// Handler for when app comes back online
const handleOnline = async () => {
  console.log('App is online - syncing pending changes');
  // Here you would implement sync logic with your backend
  // syncPendingChanges();
};

// Handler for when app goes offline
const handleOffline = () => {
  console.log('App is offline - changes will be stored locally');
};

/**
 * Clear all data from IndexedDB database
 */
export const clearIndexedDB = async (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    try {
      // Check if indexedDB is available
      if (!window.indexedDB) {
        reject(new Error('IndexedDB is not supported in this browser'));
        return;
      }

      const request = window.indexedDB.deleteDatabase(DB_NAME);

      request.onsuccess = () => {
        console.log('Successfully deleted IndexedDB database');
        resolve(true);
      };

      request.onerror = (event) => {
        console.error('Error deleting IndexedDB database', event);
        reject(new Error('Error deleting IndexedDB database'));
      };

      request.onblocked = () => {
        console.warn('Database deletion blocked - close all other tabs and try again');
        reject(new Error('Database deletion blocked - close all other tabs and try again'));
      };
    } catch (error) {
      console.error('Exception when clearing IndexedDB:', error);
      reject(error);
    }
  });
};
