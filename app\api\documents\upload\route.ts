export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { AppworksDocumentService } from '@/lib/appworks-service';

/**
 * POST handler to upload a document via Appworks SOAP API
 * This acts as a proxy to avoid CORS issues with direct API calls
 * 
 * @param request NextRequest object
 * @returns NextResponse with the document ID and URL or an error message
 */
export async function POST(request: NextRequest) {
  try {
    // Get the SAML token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const samlToken = authHeader?.replace('SAMLart ', '') || '';

    if (!samlToken) {
      return NextResponse.json(
        { error: 'Authentication token is required' },
        { status: 401 }
      );
    }

    // Get request body parameters
    const { 
      fileName, 
      description, 
      content, // Base64 encoded content
      mimeType, 
      businessWorkspaceId 
    } = await request.json();

    // Validate required parameters
    if (!fileName || !content || !mimeType || !businessWorkspaceId) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    console.log(`Uploading document ${fileName} to workspace ${businessWorkspaceId}`);

    // Use the AppworksDocumentService to upload the document
    const documentId = await AppworksDocumentService.uploadDocument({
      fileName,
      description: description || '',
      content,
      mimeType,
      businessWorkspaceId,
      samlToken
    });

    if (!documentId) {
      return NextResponse.json(
        { error: 'Failed to upload document' },
        { status: 500 }
      );
    }

    console.log(`Successfully uploaded document with ID: ${documentId}`);
    
    return NextResponse.json({
      id: documentId,
      // Note: We don't have document URL in the current response, 
      // this could be enhanced later if needed
      message: 'Document uploaded successfully'
    });
  } catch (error: any) {
    // Log detailed error information
    console.error('Error uploading document:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      data: error.response?.data
    });
    
    // Handle redirect errors specifically
    if (error.code === 'ERR_FR_TOO_MANY_REDIRECTS') {
      return NextResponse.json(
        { 
          error: 'Authentication error',
          message: 'Too many redirects when accessing Appworks API. This may indicate an authentication problem.',
          suggestion: 'Please verify your authentication credentials and try again.'
        },
        { status: 500 }
      );
    }
    
    // Handle SOAP response errors
    if (error.message && error.message.includes('SOAP')) {
      return NextResponse.json(
        { 
          error: 'SOAP API Error',
          message: 'There was an error with the SOAP API request to Appworks.',
          details: error.message
        },
        { status: 500 }
      );
    }
    
    // General error response
    return NextResponse.json(
      { 
        error: 'Failed to upload document',
        message: error.message || 'An unknown error occurred' 
      },
      { status: 500 }
    );
  }
}
