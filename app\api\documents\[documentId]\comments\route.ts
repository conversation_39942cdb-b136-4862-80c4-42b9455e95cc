import { NextResponse } from "next/server";
import { getDocumentComments, saveDocumentComment } from "@/lib/documents";
import { DocumentComment } from "@/types";

// Create a new comment
export async function POST(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  try {
    const { content, selection, versionId, userId, userName, userAvatar } = await req.json();
    
    if (!content || !userId || !userName) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    const newComment: DocumentComment = {
      id: crypto.randomUUID(),
      documentId: params.documentId,
      versionId,
      content,
      author: {
        id: userId,
        name: userName,
        avatar: userAvatar || ""
      },
      createdAt: new Date().toISOString(),
      selection,
      resolved: false
    };
    
    // Save comment
    await saveDocumentComment(newComment);
    
    return NextResponse.json(newComment);
  } catch (error) {
    console.error("Error creating comment:", error);
    return NextResponse.json(
      { error: "Failed to create comment" },
      { status: 500 }
    );
  }
}

// Get comments for a document
export async function GET(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  const searchParams = new URL(req.url).searchParams;
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = parseInt(searchParams.get("offset") || "0");
  const versionId = searchParams.get("versionId") || undefined;
  
  try {
    const comments = await getDocumentComments(params.documentId, { 
      limit, 
      offset, 
      versionId 
    });
    
    return NextResponse.json(comments);
  } catch (error) {
    console.error("Error fetching comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch comments" },
      { status: 500 }
    );
  }
}
