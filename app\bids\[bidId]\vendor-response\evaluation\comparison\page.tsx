'use client';

import { use<PERSON>ara<PERSON> } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';

type VendorScore = {
  vendorId: string;
  vendorName: string;
  technicalScore: number;
  commercialScore: number;
  presentationScore: number;
  dueDiligenceComplete: boolean;
  overallScore: number;
  status: 'pending' | 'completed' | 'shortlisted' | 'rejected';
};

type ComparisonMetric = {
  category: string;
  metrics: {
    name: string;
    vendors: {
      [key: string]: number;
    };
  }[];
};

const mockVendors: VendorScore[] = [
  {
    vendorId: 'v1',
    vendorName: 'Tech Solutions Inc.',
    technicalScore: 8.5,
    commercialScore: 7.8,
    presentationScore: 9.0,
    dueDiligenceComplete: true,
    overallScore: 8.4,
    status: 'shortlisted'
  },
  {
    vendorId: 'v2',
    vendorName: 'Global Systems Ltd.',
    technicalScore: 7.9,
    commercialScore: 8.5,
    presentationScore: 7.5,
    dueDiligenceComplete: true,
    overallScore: 8.0,
    status: 'shortlisted'
  },
  {
    vendorId: 'v3',
    vendorName: 'Innovation Corp',
    technicalScore: 6.5,
    commercialScore: 7.0,
    presentationScore: 6.8,
    dueDiligenceComplete: false,
    overallScore: 6.8,
    status: 'pending'
  }
];

const detailedMetrics: ComparisonMetric[] = [
  {
    category: 'Technical',
    metrics: [
      {
        name: 'Solution Methodology',
        vendors: { v1: 9, v2: 8, v3: 7 }
      },
      {
        name: 'Technical Capability',
        vendors: { v1: 8, v2: 8, v3: 6 }
      },
      {
        name: 'Innovation Approach',
        vendors: { v1: 8.5, v2: 7.8, v3: 6.5 }
      }
    ]
  },
  {
    category: 'Commercial',
    metrics: [
      {
        name: 'Pricing Structure',
        vendors: { v1: 7.5, v2: 8.5, v3: 7 }
      },
      {
        name: 'Cost Effectiveness',
        vendors: { v1: 8, v2: 8.5, v3: 7.5 }
      },
      {
        name: 'Financial Stability',
        vendors: { v1: 8, v2: 8.5, v3: 6.5 }
      }
    ]
  },
  {
    category: 'Presentation',
    metrics: [
      {
        name: 'Clarity',
        vendors: { v1: 9, v2: 7.5, v3: 7 }
      },
      {
        name: 'Understanding',
        vendors: { v1: 9, v2: 7.5, v3: 6.5 }
      },
      {
        name: 'Q&A Response',
        vendors: { v1: 9, v2: 7.5, v3: 7 }
      }
    ]
  }
];

export default function ComparisonPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const [selectedMetric, setSelectedMetric] = useState<string>('overall');

  const getStatusBadge = (status: VendorScore['status']) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      completed: 'bg-blue-100 text-blue-800',
      shortlisted: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    return variants[status];
  };

  const chartData = mockVendors.map(vendor => ({
    name: vendor.vendorName,
    Technical: vendor.technicalScore,
    Commercial: vendor.commercialScore,
    Presentation: vendor.presentationScore,
    Overall: vendor.overallScore
  }));

  return (
    <div className="container mx-auto py-8">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Vendor Response Comparison</CardTitle>
          <CardDescription>
            Compare evaluation results across all vendors for bid #{bidId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {/* Summary Table */}
            <ScrollArea className="h-[400px] w-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Vendor</TableHead>
                    <TableHead className="text-right">Technical</TableHead>
                    <TableHead className="text-right">Commercial</TableHead>
                    <TableHead className="text-right">Presentation</TableHead>
                    <TableHead className="text-right">Overall</TableHead>
                    <TableHead>Due Diligence</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockVendors.map((vendor) => (
                    <TableRow key={vendor.vendorId}>
                      <TableCell className="font-medium">{vendor.vendorName}</TableCell>
                      <TableCell className="text-right">{vendor.technicalScore.toFixed(1)}</TableCell>
                      <TableCell className="text-right">{vendor.commercialScore.toFixed(1)}</TableCell>
                      <TableCell className="text-right">{vendor.presentationScore.toFixed(1)}</TableCell>
                      <TableCell className="text-right">{vendor.overallScore.toFixed(1)}</TableCell>
                      <TableCell>
                        <Badge variant={vendor.dueDiligenceComplete ? 'default' : 'secondary'}>
                          {vendor.dueDiligenceComplete ? 'Complete' : 'Pending'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadge(vendor.status)}>
                          {vendor.status.charAt(0).toUpperCase() + vendor.status.slice(1)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>

            {/* Score Comparison Chart */}
            <div className="h-[400px] w-full">
              <div className="mb-4 flex space-x-2">
                <Button
                  variant={selectedMetric === 'overall' ? 'default' : 'outline'}
                  onClick={() => setSelectedMetric('overall')}
                >
                  Overall
                </Button>
                <Button
                  variant={selectedMetric === 'detailed' ? 'default' : 'outline'}
                  onClick={() => setSelectedMetric('detailed')}
                >
                  Detailed
                </Button>
              </div>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 10]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="Technical" fill="#2563eb" />
                  <Bar dataKey="Commercial" fill="#16a34a" />
                  <Bar dataKey="Presentation" fill="#ca8a04" />
                  {selectedMetric === 'overall' && (
                    <Bar dataKey="Overall" fill="#9333ea" />
                  )}
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Detailed Metrics Comparison */}
            <Card>
              <CardHeader>
                <CardTitle>Detailed Metrics Comparison</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  {detailedMetrics.map((category) => (
                    <div key={category.category} className="mb-8">
                      <h3 className="text-lg font-semibold mb-4">{category.category} Metrics</h3>
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Metric</TableHead>
                            {mockVendors.map((vendor) => (
                              <TableHead key={vendor.vendorId} className="text-right">
                                {vendor.vendorName}
                              </TableHead>
                            ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {category.metrics.map((metric) => (
                            <TableRow key={metric.name}>
                              <TableCell>{metric.name}</TableCell>
                              {mockVendors.map((vendor) => (
                                <TableCell key={vendor.vendorId} className="text-right">
                                  {metric.vendors[vendor.vendorId].toFixed(1)}
                                </TableCell>
                              ))}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ))}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
