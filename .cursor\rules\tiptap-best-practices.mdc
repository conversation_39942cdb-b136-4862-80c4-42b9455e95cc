---
description: 
globs: 
alwaysApply: true
---
description: Best practices for using Tiptap rich text editor
globs: **/*.{ts,tsx}

- Extend Tiptap's starter kit with custom extensions for specific functionality.
- Implement collaborative editing features using Tiptap's collaboration extensions.
- Use Tiptap's built-in styling options or customize with your own CSS.
- Optimize performance by lazy-loading extensions and limiting the editor's features to what's necessary.