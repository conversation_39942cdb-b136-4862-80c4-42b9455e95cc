---
description: 
globs: 
alwaysApply: true
---
description: TypeScript coding standards and type safety guidelines
globs: **/*.{ts,tsx}

- Enable strict mode in your `tsconfig.json` for better type checking.
- Use interfaces for object shapes and types for unions or intersections.
- Leverage type guards and type assertions for runtime type checking.
- Utilize generics for creating reusable components and functions.