// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom/extend-expect';

// Mock sql.js
global.SQL = {
  Database: jest.fn().mockImplementation(() => ({
    exec: jest.fn(),
    run: jest.fn(),
    prepare: jest.fn().mockReturnValue({
      bind: jest.fn().mockReturnThis(),
      step: jest.fn(),
      get: jest.fn(),
      getAsObject: jest.fn(),
      free: jest.fn(),
    }),
    close: jest.fn(),
  })),
};

// Mock crypto.randomUUID
if (!global.crypto) {
  global.crypto = {};
}

if (!global.crypto.randomUUID) {
  global.crypto.randomUUID = () => 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// Mock local storage
if (!global.localStorage) {
  global.localStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
}

// Mock IndexedDB
if (!global.indexedDB) {
  global.indexedDB = {
    open: jest.fn(),
    deleteDatabase: jest.fn(),
  };
} 