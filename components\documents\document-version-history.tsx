"use client";

import { DocumentVersion, DiffBlock } from "@/types";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  ArrowDown, 
  Clock, 
  Undo2, 
  Download, 
  Check, 
  User,
  ArrowLeftRight,
  FileText,
  Loader2,
  Plus,
  Minus,
  RefreshCw
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useState, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";

interface DocumentVersionHistoryProps {
  documentId: string;
  versions: DocumentVersion[];
  onRestore: (version: DocumentVersion) => void;
  onCompareVersions?: (sourceVersionId: string, targetVersionId: string) => Promise<any>;
  isLoadingVersions?: boolean;
}

export function DocumentVersionHistory({
  documentId,
  versions,
  onRestore,
  onCompareVersions,
  isLoadingVersions = false
}: DocumentVersionHistoryProps) {
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null);
  const [comparisonVersion, setComparisonVersion] = useState<DocumentVersion | null>(null);
  const [isComparing, setIsComparing] = useState(false);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [diffData, setDiffData] = useState<{
    additions: DiffBlock[];
    deletions: DiffBlock[];
    modifications: DiffBlock[];
  } | null>(null);
  const [isLoadingDiff, setIsLoadingDiff] = useState(false);
  const [compareTab, setCompareTab] = useState<'side-by-side' | 'unified'>('side-by-side');

  const handleRestore = (version: DocumentVersion) => {
    onRestore(version);
    setIsRestoreDialogOpen(false);
  };

  const handleCompare = async (v1: DocumentVersion, v2: DocumentVersion) => {
    setSelectedVersion(v1);
    setComparisonVersion(v2);
    setIsComparing(true);
    
    if (onCompareVersions) {
      try {
        setIsLoadingDiff(true);
        setDiffData(null);
        const result = await onCompareVersions(v1.id, v2.id);
        setDiffData(result.differences);
      } catch (error) {
        console.error("Error comparing versions:", error);
      } finally {
        setIsLoadingDiff(false);
      }
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "under_review":
        return "bg-blue-100 text-blue-800";
      case "approved":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Helper function to highlight diffs in text
  const highlightContent = (content: string, diffBlocks: DiffBlock[], type: 'addition' | 'deletion' | 'modification') => {
    if (!diffBlocks || diffBlocks.length === 0) return <pre className="whitespace-pre-wrap">{content}</pre>;
    
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    
    // Sort the diff blocks by start position
    const sortedBlocks = [...diffBlocks].sort((a, b) => a.startPosition - b.startPosition);
    
    for (const block of sortedBlocks) {
      // Add text before the diff block
      if (block.startPosition > lastIndex) {
        parts.push(<span key={`before-${block.startPosition}`}>{content.substring(lastIndex, block.startPosition)}</span>);
      }
      
      // Add the diff block with appropriate styling
      const blockContent = content.substring(block.startPosition, block.endPosition);
      const blockClass = type === 'addition' 
        ? 'bg-green-100 text-green-800 px-1 rounded' 
        : type === 'deletion' 
          ? 'bg-red-100 text-red-800 px-1 rounded line-through' 
          : 'bg-yellow-100 text-yellow-800 px-1 rounded';
          
      const blockIcon = type === 'addition' 
        ? <Plus className="inline h-3 w-3 mr-1" /> 
        : type === 'deletion' 
          ? <Minus className="inline h-3 w-3 mr-1" /> 
          : <RefreshCw className="inline h-3 w-3 mr-1" />;
          
      parts.push(
        <span key={`diff-${block.startPosition}`} className={blockClass}>
          {blockIcon}{blockContent}
        </span>
      );
      
      lastIndex = block.endPosition;
    }
    
    // Add any remaining text after the last diff block
    if (lastIndex < content.length) {
      parts.push(<span key={`after-${lastIndex}`}>{content.substring(lastIndex)}</span>);
    }
    
    return <pre className="whitespace-pre-wrap">{parts}</pre>;
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="text-xl">Version History</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        {isLoadingVersions ? (
          <div className="flex items-center justify-center h-40">
            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
            <span>Loading versions...</span>
          </div>
        ) : versions.length === 0 ? (
          <div className="p-4 text-center">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium">No versions available</h3>
            <p className="text-muted-foreground">
              Save a version of this document to see it here.
            </p>
          </div>
        ) : (
          <>
            {isComparing ? (
              <div className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">
                    Comparing Versions
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setIsComparing(false);
                      setDiffData(null);
                    }}
                  >
                    Back to list
                  </Button>
                </div>
                
                <div className="mb-4">
                  <Tabs value={compareTab} onValueChange={(value) => setCompareTab(value as 'side-by-side' | 'unified')}>
                    <TabsList className="grid w-full max-w-md grid-cols-2">
                      <TabsTrigger value="side-by-side">Side by Side</TabsTrigger>
                      <TabsTrigger value="unified">Unified View</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="side-by-side" className="mt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              Version {selectedVersion?.version} ({formatDate(selectedVersion?.metadata.createdAt || "")})
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-3 text-sm">
                            <div className="whitespace-pre-wrap border p-2 rounded-md h-80 overflow-auto">
                              {isLoadingDiff ? (
                                <div className="flex items-center justify-center h-full">
                                  <Loader2 className="h-4 w-4 animate-spin text-primary mr-2" />
                                  <span>Analyzing differences...</span>
                                </div>
                              ) : diffData ? (
                                highlightContent(
                                  selectedVersion?.content || "",
                                  diffData.deletions,
                                  'deletion'
                                )
                              ) : (
                                selectedVersion?.content || ""
                              )}
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">
                              Version {comparisonVersion?.version} ({formatDate(comparisonVersion?.metadata.createdAt || "")})
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-3 text-sm">
                            <div className="whitespace-pre-wrap border p-2 rounded-md h-80 overflow-auto">
                              {isLoadingDiff ? (
                                <div className="flex items-center justify-center h-full">
                                  <Loader2 className="h-4 w-4 animate-spin text-primary mr-2" />
                                  <span>Analyzing differences...</span>
                                </div>
                              ) : diffData ? (
                                highlightContent(
                                  comparisonVersion?.content || "",
                                  diffData.additions,
                                  'addition'
                                )
                              ) : (
                                comparisonVersion?.content || ""
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="unified" className="mt-4">
                      <Card>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm font-medium">
                            Changes from Version {selectedVersion?.version} to Version {comparisonVersion?.version}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="p-3 text-sm">
                          <div className="whitespace-pre-wrap border p-2 rounded-md h-80 overflow-auto">
                            {isLoadingDiff ? (
                              <div className="flex items-center justify-center h-full">
                                <Loader2 className="h-4 w-4 animate-spin text-primary mr-2" />
                                <span>Analyzing differences...</span>
                              </div>
                            ) : diffData ? (
                              <div className="space-y-2">
                                <div className="flex items-center text-sm">
                                  <div className="flex items-center mr-4">
                                    <span className="inline-block w-3 h-3 bg-green-100 rounded mr-1"></span>
                                    <span>Additions ({diffData.additions.length})</span>
                                  </div>
                                  <div className="flex items-center mr-4">
                                    <span className="inline-block w-3 h-3 bg-red-100 rounded mr-1"></span>
                                    <span>Deletions ({diffData.deletions.length})</span>
                                  </div>
                                  <div className="flex items-center">
                                    <span className="inline-block w-3 h-3 bg-yellow-100 rounded mr-1"></span>
                                    <span>Modifications ({diffData.modifications.length})</span>
                                  </div>
                                </div>
                                
                                {diffData.modifications.length > 0 && (
                                  <div className="mb-4">
                                    <h4 className="font-medium mb-2">Modifications:</h4>
                                    <div className="border p-2 rounded-md bg-gray-50">
                                      {diffData.modifications.map((mod, i) => (
                                        <div key={`mod-${i}`} className="mb-2 last:mb-0">
                                          <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                            <RefreshCw className="inline h-3 w-3 mr-1" />
                                            {mod.content}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                                
                                <div className="border p-2 rounded-md">
                                  {highlightContent(
                                    comparisonVersion?.content || "",
                                    [...diffData.additions, ...diffData.deletions],
                                    'addition'
                                  )}
                                </div>
                              </div>
                            ) : (
                              <p className="text-center text-muted-foreground">
                                Select two versions to compare and see the changes highlighted here.
                              </p>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </div>
                
                {diffData && (
                  <div className="bg-gray-50 p-4 rounded-md border">
                    <h4 className="font-medium mb-2">Change Summary:</h4>
                    <ul className="space-y-1 text-sm">
                      {diffData.additions.length > 0 && (
                        <li className="flex items-center">
                          <Plus className="h-4 w-4 text-green-600 mr-2" />
                          {diffData.additions.length} {diffData.additions.length === 1 ? 'addition' : 'additions'}
                        </li>
                      )}
                      {diffData.deletions.length > 0 && (
                        <li className="flex items-center">
                          <Minus className="h-4 w-4 text-red-600 mr-2" />
                          {diffData.deletions.length} {diffData.deletions.length === 1 ? 'deletion' : 'deletions'}
                        </li>
                      )}
                      {diffData.modifications.length > 0 && (
                        <li className="flex items-center">
                          <RefreshCw className="h-4 w-4 text-yellow-600 mr-2" />
                          {diffData.modifications.length} {diffData.modifications.length === 1 ? 'modification' : 'modifications'}
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <ScrollArea className="h-[calc(100vh-300px)]">
                <div className="p-4 space-y-4">
                  {versions.map((version, index) => (
                    <Card key={version.id} className="overflow-hidden">
                      <div className="border-l-4 border-primary p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="text-lg font-semibold flex items-center">
                              Version {version.version}
                              {index === 0 && (
                                <Badge className="ml-2 bg-primary/20 text-primary border-primary/20">
                                  Latest
                                </Badge>
                              )}
                            </h3>
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <Clock className="h-3.5 w-3.5 mr-1" />
                              {formatDate(version.metadata.createdAt)}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <User className="h-3.5 w-3.5 mr-1" />
                              {version.metadata.createdBy.name}
                            </div>
                            {version.metadata.commitMessage && (
                              <p className="mt-2 text-sm">
                                &quot;{version.metadata.commitMessage}&quot;
                              </p>
                            )}
                            <Badge
                              className={`mt-2 ${getStatusBadgeColor(version.metadata.status)}`}
                              variant="outline"
                            >
                              {version.metadata.status.replace("_", " ")}
                            </Badge>
                          </div>
                          <div className="flex gap-2">
                            <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
                              <DialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8"
                                  onClick={() => setSelectedVersion(version)}
                                >
                                  <Undo2 className="h-3.5 w-3.5 mr-1.5" />
                                  Restore
                                </Button>
                              </DialogTrigger>
                              <DialogContent>
                                <DialogHeader>
                                  <DialogTitle>Restore Version</DialogTitle>
                                  <DialogDescription>
                                    Are you sure you want to restore version {selectedVersion?.version}? This will replace the current content of the editor.
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="flex justify-end gap-2 mt-4">
                                  <Button
                                    variant="outline"
                                    onClick={() => setIsRestoreDialogOpen(false)}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    onClick={() => selectedVersion && handleRestore(selectedVersion)}
                                  >
                                    <Check className="h-4 w-4 mr-2" />
                                    Restore
                                  </Button>
                                </div>
                              </DialogContent>
                            </Dialog>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8"
                              onClick={() => {
                                // Compare with the previous version if available, otherwise compare with the next version
                                const compareWithVersion = versions[index + 1] || versions[index - 1];
                                if (compareWithVersion) {
                                  handleCompare(version, compareWithVersion);
                                }
                              }}
                              disabled={versions.length <= 1}
                            >
                              <ArrowLeftRight className="h-3.5 w-3.5 mr-1.5" />
                              Compare
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8"
                            >
                              <Download className="h-3.5 w-3.5 mr-1.5" />
                              Download
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
