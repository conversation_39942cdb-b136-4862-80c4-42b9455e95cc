import { NextResponse } from "next/server";
import { DocumentVersionControl, getDocumentVersions, getLatestVersion, saveDocumentVersion } from "@/lib/documents";

const versionControl = new DocumentVersionControl();

// Create a new version
export async function POST(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  try {
    const { content, commitMessage, userId, userName, userRole } = await req.json();
    
    if (!content || !commitMessage || !userId || !userName) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Get latest version
    const latestVersion = await getLatestVersion(params.documentId);
    
    // Create new version
    const newVersion = await versionControl.createVersion({
      documentId: params.documentId,
      content,
      previousVersion: latestVersion,
      commitMessage,
      userId,
      userName,
      userRole
    });
    
    // Save version
    await saveDocumentVersion(newVersion);
    
    return NextResponse.json(newVersion);
  } catch (error) {
    console.error("Error creating version:", error);
    return NextResponse.json(
      { error: "Failed to create version" },
      { status: 500 }
    );
  }
}

// Get versions for a document
export async function GET(
  req: Request,
  { params }: { params: { documentId: string } }
) {
  const searchParams = new URL(req.url).searchParams;
  const limit = parseInt(searchParams.get("limit") || "10");
  const offset = parseInt(searchParams.get("offset") || "0");
  
  try {
    const versions = await getDocumentVersions(params.documentId, { limit, offset });
    return NextResponse.json(versions);
  } catch (error) {
    console.error("Error fetching versions:", error);
    return NextResponse.json(
      { error: "Failed to fetch versions" },
      { status: 500 }
    );
  }
}
