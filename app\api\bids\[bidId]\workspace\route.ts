export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { AppworksDocumentService } from '@/lib/appworks-service';

/**
 * GET handler to retrieve the BusinessWorkspaceId for a bid
 * 
 * This acts as a proxy to the Appworks API to avoid CORS issues
 * 
 * @param request NextRequest object
 * @param params Contains the bidId from the URL
 * @returns NextResponse with the BusinessWorkspaceId or an error message
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { bidId: string } }
) {
  const bidId = params.bidId;
  
  if (!bidId) {
    return NextResponse.json(
      { error: 'Bid ID is required' },
      { status: 400 }
    );
  }

  // Get the SAML token from the Authorization header
  const authHeader = request.headers.get('Authorization');
  const samlToken = authHeader?.replace('SAMLart ', '') || '';
  
  if (!samlToken) {
    return NextResponse.json(
      { error: 'Authentication token is required' },
      { status: 401 }
    );
  }

  try {
    // Log the request information
    console.log(`Retrieving BusinessWorkspaceId for bid ${bidId}`);
    
    // Get the BusinessWorkspaceId for the bid using the static method
    const businessWorkspaceId = await AppworksDocumentService.getBidBusinessWorkspaceId(bidId, samlToken);
    
    if (!businessWorkspaceId) {
      return NextResponse.json(
        { error: 'Failed to retrieve business workspace ID' },
        { status: 404 }
      );
    }
    
    console.log(`Successfully retrieved BusinessWorkspaceId: ${businessWorkspaceId}`);
    return NextResponse.json({ businessWorkspaceId });
  } catch (error: any) {
    // Log detailed error information
    console.error('Error retrieving business workspace ID:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      data: error.response?.data
    });
    
    // Handle redirect errors specifically
    if (error.code === 'ERR_FR_TOO_MANY_REDIRECTS') {
      return NextResponse.json(
        { 
          error: 'Authentication error',
          message: 'Too many redirects when accessing Appworks API. This may indicate an authentication problem.',
          suggestion: 'Please verify your authentication credentials and try again.'
        },
        { status: 500 }
      );
    }
    
    // General error response
    return NextResponse.json(
      { 
        error: 'Failed to retrieve business workspace ID',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
