---
description: 
globs: 
alwaysApply: true
---
description: Best practices for using Tailwind CSS in your project
globs: **/*.{ts,tsx,css}

- Use utility-first classes for styling, but create custom components for complex UI elements.
- Utilize Tailwind's responsive utilities for mobile-first design.
- Keep your `tailwind.config.js` organized and use it to extend or override default styles.
- Use the `@apply` directive in custom CSS to maintain consistency with Tailwind's utility classes.
