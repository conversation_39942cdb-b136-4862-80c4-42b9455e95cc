'use client';

import { useParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Loader2, AlertTriangle, CheckCircle2, XCircle, FileCheck, Info, MoreVertical, Filter, Download, Upload, ChevronDown } from 'lucide-react';
import { useState } from 'react';

interface AutomatedCheck {
  type: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: string;
}

interface VendorResponse {
  id: string;
  vendorId: string;
  companyName: string;
  submittedAt: string;
  status: 'pending' | 'screening' | 'approved' | 'rejected';
  documents: {
    name: string;
    type: string;
    status: 'pending' | 'verified' | 'rejected';
    issues?: string[];
    automatedChecks?: AutomatedCheck[];
    lastModified?: string;
    size?: number;
    version?: string;
  }[];
  compliance: {
    category: string;
    items: {
      id: string;
      requirement: string;
      status: 'pending' | 'compliant' | 'non-compliant';
      notes?: string;
      automatedChecks?: AutomatedCheck[];
      evidence?: string;
      lastVerified?: string;
    }[];
  }[];
  selectedForBulkAction?: boolean;
}

// TODO: Replace with actual API calls when vendor response API is implemented
// This mock data is temporary and should be replaced with real data from the API
const mockResponses: VendorResponse[] = [
  {
    id: '1',
    vendorId: 'v1',
    companyName: 'Tech Solutions Inc.',
    submittedAt: '2025-03-13T14:00:00Z',
    status: 'screening',
    documents: [
      {
        name: 'Technical Proposal.pdf',
        type: 'technical',
        status: 'verified',
      },
      {
        name: 'Financial Proposal.pdf',
        type: 'financial',
        status: 'pending',
      },
      {
        name: 'Company Profile.pdf',
        type: 'profile',
        status: 'rejected',
        issues: ['Document is outdated', 'Missing certification details'],
      },
    ],
    compliance: [
      {
        category: 'Technical Requirements',
        items: [
          {
            id: 't1',
            requirement: 'API Integration Capability',
            status: 'compliant',
            notes: 'Demonstrated experience with similar integrations',
          },
          {
            id: 't2',
            requirement: 'Security Standards',
            status: 'non-compliant',
            notes: 'Missing ISO 27001 certification',
          },
        ],
      },
      {
        category: 'Commercial Requirements',
        items: [
          {
            id: 'c1',
            requirement: 'Pricing Structure',
            status: 'pending',
          },
          {
            id: 'c2',
            requirement: 'Payment Terms',
            status: 'compliant',
          },
        ],
      },
    ],
  },
  // Add more mock responses as needed
];

const screeningSchema = z.object({
  notes: z.string().optional(),
});

type ScreeningForm = z.infer<typeof screeningSchema>;

export default function ScreeningPage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [responses, setResponses] = useState<VendorResponse[]>(mockResponses);
  const [selectedResponse, setSelectedResponse] = useState<VendorResponse | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    documentStatus: 'all',
    complianceStatus: 'all',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [showAutomatedChecks, setShowAutomatedChecks] = useState(false);

  const form = useForm<ScreeningForm>({
    resolver: zodResolver(screeningSchema),
  });

  const handleUpdateCompliance = async (
    responseId: string,
    categoryIndex: number,
    itemId: string,
    status: 'pending' | 'compliant' | 'non-compliant',
    notes?: string
  ) => {
    try {
      // TODO: Implement API call to update compliance status
      setResponses((prev) =>
        prev.map((response) => {
          if (response.id === responseId) {
            const updatedCompliance = [...response.compliance];
            const itemIndex = updatedCompliance[categoryIndex].items.findIndex(
              (item) => item.id === itemId
            );
            if (itemIndex !== -1) {
              updatedCompliance[categoryIndex].items[itemIndex] = {
                ...updatedCompliance[categoryIndex].items[itemIndex],
                status,
                notes,
              };
            }
            return { ...response, compliance: updatedCompliance };
          }
          return response;
        })
      );

      toast({
        title: "Status Updated",
        description: "Compliance status has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating compliance status:', error);
      toast({
        title: "Update Failed",
        description: "There was an error updating the compliance status.",
        variant: "destructive",
      });
    }
  };

  const handleUpdateDocumentStatus = async (
    responseId: string,
    documentName: string,
    status: 'pending' | 'verified' | 'rejected',
    issues?: string[]
  ) => {
    try {
      // TODO: Implement API call to update document status
      setResponses((prev) =>
        prev.map((response) => {
          if (response.id === responseId) {
            const updatedDocuments = response.documents.map((doc) =>
              doc.name === documentName ? { ...doc, status, issues } : doc
            );
            return { ...response, documents: updatedDocuments };
          }
          return response;
        })
      );

      toast({
        title: "Document Status Updated",
        description: "Document verification status has been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating document status:', error);
      toast({
        title: "Update Failed",
        description: "There was an error updating the document status.",
        variant: "destructive",
      });
    }
  };

  const handleBulkAction = async (action: 'verify' | 'reject' | 'reset') => {
    const selectedResponses = responses.filter(r => r.selectedForBulkAction);
    if (selectedResponses.length === 0) {
      toast({
        title: "No Items Selected",
        description: "Please select items to perform bulk actions.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // TODO: Implement API call for bulk actions
      const updatedResponses = responses.map(response => {
        if (response.selectedForBulkAction) {
          const newStatus = action === 'verify' ? 'verified' :
                          action === 'reject' ? 'rejected' : 'pending';

          return {
            ...response,
            documents: response.documents.map(doc => ({
              ...doc,
              status: newStatus as 'pending' | 'verified' | 'rejected',
            })),
            selectedForBulkAction: false,
          };
        }
        return response;
      });

      setResponses(updatedResponses);
      toast({
        title: "Bulk Action Complete",
        description: `Successfully ${action}ed ${selectedResponses.length} items.`,
      });
    } catch (error) {
      console.error('Error performing bulk action:', error);
      toast({
        title: "Action Failed",
        description: "There was an error performing the bulk action.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const runAutomatedChecks = async (responseId: string) => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call for automated checks
      const mockChecks: AutomatedCheck[] = [
        {
          type: 'format',
          status: 'passed',
          message: 'Document format is valid',
          details: 'PDF version 1.7, compliant with ISO 32000-1',
        },
        {
          type: 'completeness',
          status: 'warning',
          message: 'Missing optional sections',
          details: 'Executive summary section not found',
        },
        {
          type: 'compliance',
          status: 'failed',
          message: 'Required certifications missing',
          details: 'ISO 27001 certification not found in documents',
        },
      ];

      setResponses(prev =>
        prev.map(response =>
          response.id === responseId
            ? {
                ...response,
                documents: response.documents.map(doc => ({
                  ...doc,
                  automatedChecks: mockChecks,
                })),
              }
            : response
        )
      );

      toast({
        title: "Automated Checks Complete",
        description: "Document analysis has been completed.",
      });
    } catch (error) {
      console.error('Error running automated checks:', error);
      toast({
        title: "Check Failed",
        description: "There was an error running automated checks.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateProgress = (response: VendorResponse) => {
    const totalItems =
      response.documents.length +
      response.compliance.reduce((acc, cat) => acc + cat.items.length, 0);

    const completedItems =
      response.documents.filter((doc) => doc.status !== 'pending').length +
      response.compliance.reduce(
        (acc, cat) =>
          acc +
          cat.items.filter((item) => item.status !== 'pending').length,
        0
      );

    return (completedItems / totalItems) * 100;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
      case 'compliant':
        return 'bg-green-500';
      case 'rejected':
      case 'non-compliant':
        return 'bg-red-500';
      default:
        return 'bg-yellow-500';
    }
  };

  const filteredResponses = responses.filter(response => {
    if (filters.status !== 'all' && response.status !== filters.status) return false;
    if (searchTerm && !response.companyName.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });

  return (
    <div className="container mx-auto py-8">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Initial Screening</CardTitle>
              <CardDescription>
                Review vendor responses for compliance and document verification.
              </CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex w-[200px]">
                <Input
                  placeholder="Search vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <Filter className="mr-2 h-4 w-4" />
                    Filters
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Filter By Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setFilters(f => ({ ...f, status: 'all' }))}>
                    All Status
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters(f => ({ ...f, status: 'pending' }))}>
                    Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters(f => ({ ...f, status: 'screening' }))}>
                    In Screening
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters(f => ({ ...f, status: 'approved' }))}>
                    Approved
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilters(f => ({ ...f, status: 'rejected' }))}>
                    Rejected
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    Bulk Actions
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleBulkAction('verify')}>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Verify Selected
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkAction('reject')}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject Selected
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleBulkAction('reset')}>
                    Reset Status
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[800px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[30px]">
                    <Checkbox
                      checked={responses.every(r => r.selectedForBulkAction)}
                      onCheckedChange={(checked) => {
                        setResponses(prev =>
                          prev.map(r => ({ ...r, selectedForBulkAction: !!checked }))
                        );
                      }}
                    />
                  </TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Submission Date</TableHead>
                  <TableHead>Progress</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResponses.map((response) => (
                  <TableRow key={response.id}>
                    <TableCell>
                      <Checkbox
                        checked={response.selectedForBulkAction}
                        onCheckedChange={(checked) => {
                          setResponses(prev =>
                            prev.map(r =>
                              r.id === response.id
                                ? { ...r, selectedForBulkAction: !!checked }
                                : r
                            )
                          );
                        }}
                      />
                    </TableCell>
                    <TableCell>{response.companyName}</TableCell>
                    <TableCell>
                      {new Date(response.submittedAt).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <div className="w-[200px]">
                        <Progress value={calculateProgress(response)} />
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary" className={getStatusColor(response.status)}>
                        {response.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              onClick={() => setSelectedResponse(response)}
                            >
                              Review
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl">
                            <DialogHeader>
                              <DialogTitle>Review Submission</DialogTitle>
                              <DialogDescription>
                                Review and verify vendor documents and compliance requirements.
                              </DialogDescription>
                            </DialogHeader>
                            <Tabs defaultValue="documents" className="mt-4">
                              <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger value="documents">Documents</TabsTrigger>
                                <TabsTrigger value="compliance">Compliance</TabsTrigger>
                              </TabsList>
                              <TabsContent value="documents">
                                <div className="space-y-4">
                                  <div className="flex items-center justify-between">
                                    <Button
                                      variant="outline"
                                      onClick={() => runAutomatedChecks(response.id)}
                                      disabled={isSubmitting}
                                    >
                                      {isSubmitting ? (
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                      ) : (
                                        <FileCheck className="mr-2 h-4 w-4" />
                                      )}
                                      Run Automated Checks
                                    </Button>
                                    <Button
                                      variant="outline"
                                      onClick={() => setShowAutomatedChecks(!showAutomatedChecks)}
                                    >
                                      {showAutomatedChecks ? 'Hide' : 'Show'} Check Results
                                    </Button>
                                  </div>
                                  {response.documents.map((doc) => (
                                    <Card key={doc.name}>
                                      <CardContent className="pt-6">
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center space-x-4">
                                            <FileCheck className="h-5 w-5" />
                                            <div>
                                              <p className="font-medium">{doc.name}</p>
                                              <div className="flex items-center space-x-2 text-sm text-gray-500">
                                                <span>Type: {doc.type}</span>
                                                {doc.version && (
                                                  <>
                                                    <span>•</span>
                                                    <span>Version: {doc.version}</span>
                                                  </>
                                                )}
                                                {doc.size && (
                                                  <>
                                                    <span>•</span>
                                                    <span>Size: {(doc.size / 1024 / 1024).toFixed(2)} MB</span>
                                                  </>
                                                )}
                                              </div>
                                            </div>
                                          </div>
                                          <div className="flex items-center space-x-2">
                                            <Button variant="outline" size="sm">
                                              <Download className="mr-2 h-4 w-4" />
                                              Download
                                            </Button>
                                            <Select
                                              value={doc.status}
                                              onValueChange={(value: 'pending' | 'verified' | 'rejected') =>
                                                handleUpdateDocumentStatus(response.id, doc.name, value)
                                              }
                                            >
                                              <SelectTrigger className="w-[130px]">
                                                <SelectValue />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectItem value="pending">Pending</SelectItem>
                                                <SelectItem value="verified">Verified</SelectItem>
                                                <SelectItem value="rejected">Rejected</SelectItem>
                                              </SelectContent>
                                            </Select>
                                          </div>
                                        </div>
                                        {showAutomatedChecks && doc.automatedChecks && (
                                          <div className="mt-4 space-y-2">
                                            {doc.automatedChecks.map((check, index) => (
                                              <div
                                                key={index}
                                                className={`p-4 rounded-md ${
                                                  check.status === 'passed'
                                                    ? 'bg-green-50'
                                                    : check.status === 'warning'
                                                    ? 'bg-yellow-50'
                                                    : 'bg-red-50'
                                                }`}
                                              >
                                                <div className="flex items-center space-x-2">
                                                  {check.status === 'passed' ? (
                                                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                  ) : check.status === 'warning' ? (
                                                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                                  ) : (
                                                    <XCircle className="h-4 w-4 text-red-500" />
                                                  )}
                                                  <span className="font-medium">{check.message}</span>
                                                </div>
                                                {check.details && (
                                                  <p className="mt-1 text-sm text-gray-600">
                                                    {check.details}
                                                  </p>
                                                )}
                                              </div>
                                            ))}
                                          </div>
                                        )}
                                        {doc.issues && doc.issues.length > 0 && (
                                          <div className="mt-4 p-4 bg-red-50 rounded-md">
                                            <div className="flex items-center space-x-2 text-red-600">
                                              <AlertTriangle className="h-4 w-4" />
                                              <span className="font-medium">Issues Found:</span>
                                            </div>
                                            <ul className="mt-2 list-disc list-inside text-sm text-red-600">
                                              {doc.issues.map((issue, index) => (
                                                <li key={index}>{issue}</li>
                                              ))}
                                            </ul>
                                          </div>
                                        )}
                                      </CardContent>
                                    </Card>
                                  ))}
                                </div>
                              </TabsContent>
                              <TabsContent value="compliance">
                                <div className="space-y-6">
                                  {response.compliance.map((category, categoryIndex) => (
                                    <Card key={category.category}>
                                      <CardHeader>
                                        <CardTitle className="text-lg">
                                          {category.category}
                                        </CardTitle>
                                      </CardHeader>
                                      <CardContent>
                                        <div className="space-y-4">
                                          {category.items.map((item) => (
                                            <div
                                              key={item.id}
                                              className="flex items-start justify-between border-b pb-4"
                                            >
                                              <div className="space-y-1">
                                                <p className="font-medium">
                                                  {item.requirement}
                                                </p>
                                                {item.notes && (
                                                  <p className="text-sm text-gray-500">
                                                    {item.notes}
                                                  </p>
                                                )}
                                              </div>
                                              <Select
                                                value={item.status}
                                                onValueChange={(value: 'pending' | 'compliant' | 'non-compliant') =>
                                                  handleUpdateCompliance(
                                                    response.id,
                                                    categoryIndex,
                                                    item.id,
                                                    value
                                                  )
                                                }
                                              >
                                                <SelectTrigger className="w-[150px]">
                                                  <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                  <SelectItem value="pending">Pending</SelectItem>
                                                  <SelectItem value="compliant">Compliant</SelectItem>
                                                  <SelectItem value="non-compliant">
                                                    Non-Compliant
                                                  </SelectItem>
                                                </SelectContent>
                                              </Select>
                                            </div>
                                          ))}
                                        </div>
                                      </CardContent>
                                    </Card>
                                  ))}
                                </div>
                              </TabsContent>
                            </Tabs>
                          </DialogContent>
                        </Dialog>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => runAutomatedChecks(response.id)}
                            >
                              <FileCheck className="mr-2 h-4 w-4" />
                              Run Checks
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 h-4 w-4" />
                              Download All
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
