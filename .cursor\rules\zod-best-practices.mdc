---
description: 
globs: 
alwaysApply: true
---
description: Best practices for schema validation with Zod
globs: **/*.{ts,tsx,js}

- Define schemas for your data structures to ensure type safety and validation.
- Use <PERSON>od's built-in validation methods for common data types and patterns.
- Implement custom validation logic using <PERSON><PERSON>'s `refine` method.
- Utilize <PERSON><PERSON>'s integration with React Hook Form for seamless form validation.
