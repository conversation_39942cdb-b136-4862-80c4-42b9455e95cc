export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { headers } from 'next/headers';
import { DOMParser } from '@xmldom/xmldom';

// Define allowed origins
const allowedOrigins = [
  process.env.NEXT_PUBLIC_APP_URL,
  'http://localhost:3000',
  'http://dbsapp.dcxeim.local:81'
];

// SAML Response interfaces
interface SAMLResponseMetadata {
  status: number;
  statusText: string;
  contentType: string;
  dataType: string;
  timestamp: string;
}

interface SAMLParseResult {
  artifact?: string;
  responseElement?: string;
  availableElements: string[];
  error?: string;
}

// XML namespace constants
const SAML_NAMESPACES = {
  SAML: 'urn:oasis:names:tc:SAML:1.0:assertion',
  SAMLP: 'urn:oasis:names:tc:SAML:1.0:protocol'
};

export async function OPTIONS() {
  const headersList = headers();
  const origin = headersList.get('origin') || '';

  // Check if the origin is allowed
  if (allowedOrigins.includes(origin)) {
    return new NextResponse(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, SOAPAction',
        'Access-Control-Max-Age': '86400',
      },
    });
  }
  return new NextResponse(null, { status: 204 });
}

  /**
   * Proxies the POST request to the AppWorks authentication endpoint,
   * passing through the SOAP request body and headers.
   * 
   * The response is logged in detail for debugging purposes,
   * and the SAML AssertionArtifact is extracted and logged separately.
   * 
   * If the request is not from an allowed origin, the response is
   * returned without the Access-Control-Allow-Origin header.
   * 
   * If the request fails, an error response is returned with a 500 status,
   * containing the error message and details.
   */
export async function POST(request: NextRequest) {
  try {
    const headersList = headers();
    const origin = headersList.get('origin') || '';

    const body = await request.text();
    const appWorksUrl = process.env.NEXT_PUBLIC_APPWORKS_AUTH_URL!;

    const response = await axios.post(appWorksUrl, body, {
      headers: {
        'Content-Type': 'text/xml',
        'SOAPAction': 'urn:oasis:names:tc:SAML:1.0:protocol:AuthenticationQuery'
      },
      proxy: false,
    });

    // Capture response metadata
    const responseMetadata: SAMLResponseMetadata = {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers['content-type'] || 'unknown',
      dataType: typeof response.data,
      timestamp: new Date().toISOString()
    };

    // Log raw response with clear boundaries
    console.log('\n========== SAML Response Debug Info ==========');
    console.log('Response Metadata:', responseMetadata);
    console.log('\n---------- Raw XML Response ----------');
    console.log(response.data);
    console.log('---------- End Raw XML Response ----------\n');

    // Extract the artifact using the exact XML path
    const responseData = response.data;
    if (typeof responseData === 'string') {
      const match = responseData.match(/<samlp:AssertionArtifact[^>]*>([^]*?)<\/samlp:AssertionArtifact>/);
      if (match && match[1]) {
        const artifact = match[1].trim();
        console.log('\n=== SAML AssertionArtifact ===');
        console.log(artifact);
        console.log('=== End AssertionArtifact ===\n');
      }
    }

    const responseHeaders: Record<string, string> = {
      'Content-Type': 'text/xml',
    };

    if (allowedOrigins.includes(origin)) {
      responseHeaders['Access-Control-Allow-Origin'] = origin;
    }

    return new NextResponse(response.data, {
      status: response.status,
      headers: responseHeaders,
    });
  } catch (error: any) {
    console.error('AppWorks proxy error:', {
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Failed to communicate with AppWorks service',
        details: error.message
      },
      { status: 500 }
    );
  }
}

  /**
   * Parses a SAML response XML string and extracts the SAML artifact or response element.
   * 
   * @param {string} xmlData - The XML string to parse.
   * 
   * @returns {Promise<SAMLParseResult>} A promise that resolves with a SAMLParseResult object,
   * containing the extracted artifact value, a list of available elements, and an error message
   * if the parsing fails.
   */
async function parseSAMLResponse(xmlData: string): Promise<SAMLParseResult> {
  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlData, 'text/xml');
    
    // Collect all element names
    const elements = xmlDoc.getElementsByTagName('*');
    const availableElements = Array.from(elements).map(el => el.nodeName);
    
    // Try different possible artifact tags with namespaces
    const possibleTags = [
      'AssertionArtifact',
      'saml:AssertionArtifact',
      'samlp:AssertionArtifact',
      'saml1:AssertionArtifact',
      'saml1p:AssertionArtifact'
    ];

    for (const tag of possibleTags) {
      const artifactElements = xmlDoc.getElementsByTagName(tag);
      if (artifactElements.length > 0 && artifactElements[0].textContent) {
        return {
          artifact: artifactElements[0].textContent,
          availableElements
        };
      }
    }

    // Check for Response element if no artifact found
    const responseElements = [
      'Response',
      'samlp:Response',
      'saml1p:Response'
    ].map(tag => xmlDoc.getElementsByTagName(tag)[0]).filter(Boolean);

    if (responseElements.length > 0) {
      return {
        responseElement: responseElements[0].textContent || undefined,
        availableElements
      };
    }

    return {
      availableElements,
      error: 'No SAML artifact or response element found'
    };
  } catch (error: any) {
    return {
      availableElements: [],
      error: `Failed to parse SAML response: ${error.message}`
    };
  }
}

/**
 * Logs the results of parsing a SAML response XML string to the console, including
 * any extracted artifact value, response element, parse errors, and a list of
 * available XML elements.
 * 
 * @param {SAMLParseResult} result - The result of parsing a SAML response XML string.
 */
function logParseResult(result: SAMLParseResult) {
  console.log('\n---------- SAML Parse Results ----------');
  
  if (result.artifact) {
    console.log('Found SAML Artifact:', result.artifact);
  }
  
  if (result.responseElement) {
    console.log('Found SAML Response Element:', result.responseElement);
  }
  
  if (result.error) {
    console.log('Parse Error:', result.error);
  }
  
  console.log('\nAvailable XML Elements:');
  result.availableElements.forEach(el => console.log(`- ${el}`));
  
  console.log('========== End SAML Debug Info ==========\n');
}
