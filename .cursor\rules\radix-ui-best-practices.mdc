---
description: 
globs: 
alwaysApply: true
---
description: Best practices for using Radix UI components
globs: **/*.{ts,tsx}

- Use Radix UI primitives as building blocks for custom components.
- Follow Radix UI's accessibility guidelines to ensure your UI is inclusive.
- Customize Radix UI components using the `asChild` prop for better composition.
- Leverage Radix UI's state management utilities for complex component interactions.
