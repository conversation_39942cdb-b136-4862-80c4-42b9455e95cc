---
description: 
globs: 
alwaysApply: true
---
description: Best practices for working with XML using xmldom
globs: **/*.{ts,tsx,js,jsx}

- Use xmldom's DOMParser for parsing XML strings
- Utilize xmldom's XMLSerializer for converting DOM to XML strings
- Implement error handling for malformed XML
- Use xmldom's namespace-aware methods for working with XML namespaces
- Optimize performance by reusing DOM objects when possible