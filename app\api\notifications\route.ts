import { NextResponse } from 'next/server';

interface Notification {
  id: string;
  vendorId: string;
  type: 'submission' | 'query' | 'screening' | 'automated_check' | 'status_update';
  title: string;
  message: string;
  status: 'unread' | 'read';
  createdAt: string;
  metadata?: {
    bidId?: string;
    queryId?: string;
    documentId?: string;
    checkResults?: {
      passed: number;
      failed: number;
      warnings: number;
    };
  };
}

// Mock notifications - replace with actual database calls
const notifications: Notification[] = [
  {
    id: '1',
    vendorId: 'v1',
    type: 'submission',
    title: 'Response Submitted Successfully',
    message: 'Your bid response has been received and is currently under review.',
    status: 'unread',
    createdAt: '2025-03-14T09:00:00Z',
    metadata: {
      bidId: 'bid123',
    },
  },
  {
    id: '2',
    vendorId: 'v1',
    type: 'query',
    title: 'New Response to Your Query',
    message: 'Your question about API integration requirements has been answered.',
    status: 'unread',
    createdAt: '2025-03-14T10:00:00Z',
    metadata: {
      bidId: 'bid123',
      queryId: 'q1',
    },
  },
];

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const vendorId = searchParams.get('vendorId');
  const type = searchParams.get('type');
  const status = searchParams.get('status');

  let filteredNotifications = [...notifications];

  if (vendorId) {
    filteredNotifications = filteredNotifications.filter(n => n.vendorId === vendorId);
  }

  if (type) {
    filteredNotifications = filteredNotifications.filter(n => n.type === type);
  }

  if (status) {
    filteredNotifications = filteredNotifications.filter(n => n.status === status);
  }

  return NextResponse.json(filteredNotifications);
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { vendorId, type, title, message, metadata } = body;

    if (!vendorId || !type || !title || !message) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const newNotification: Notification = {
      id: Date.now().toString(),
      vendorId,
      type,
      title,
      message,
      status: 'unread',
      createdAt: new Date().toISOString(),
      metadata,
    };

    // TODO: Replace with actual database insert
    notifications.push(newNotification);

    return NextResponse.json(newNotification);
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: Request) {
  try {
    const body = await request.json();
    const { id, status } = body;

    if (!id || !status) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const notification = notifications.find(n => n.id === id);
    if (!notification) {
      return NextResponse.json(
        { error: 'Notification not found' },
        { status: 404 }
      );
    }

    notification.status = status;

    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}
