"use client";

import React from 'react';
import { useState } from "react";
import { Editor } from "@tiptap/react";
import { 
  Bold, 
  Italic, 
  Underline, 
  List, 
  ListOrdered, 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  AlignJustify,
  Link as LinkIcon,
  Image,
  Heading1,
  Heading2,
  Heading3,
  Table,
  RowsIcon,
  ColumnsIcon,
  Trash2
} from "lucide-react";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/components/ui/popover";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ToolbarButtonProps {
  children: React.ReactNode;
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  isActive: boolean;
  title: string;
}

function ToolbarButton({ 
  children, 
  onClick, 
  isActive, 
  title 
}: ToolbarButtonProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={(e) => {
            e.preventDefault();
            // Focus the editor and then run the command
            onClick(e);
          }}
          title={title}
          aria-label={title}
          tabIndex={0}
          className={`h-8 w-8 p-0 ${isActive ? 'bg-muted' : ''}`}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              // Create a synthetic mouse event to pass to onClick
              onClick({
                ...e,
                preventDefault: () => e.preventDefault(),
                currentTarget: e.currentTarget,
                target: e.target,
              } as unknown as React.MouseEvent<HTMLButtonElement>);
            }
          }}
        >
          {children}
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom">
        <p>{title}</p>
      </TooltipContent>
    </Tooltip>
  );
}

interface DocumentToolbarProps {
  editor: Editor | null;
}

export function DocumentToolbar({ editor }: DocumentToolbarProps) {
  const [linkUrl, setLinkUrl] = useState("");
  const [linkOpen, setLinkOpen] = useState(false);

  if (!editor) {
    console.warn("DocumentToolbar: Editor instance is null or undefined");
    return null;
  }

  // Log editor capabilities
  console.log("Editor capabilities:",
    "Can toggle bullet list:", !!editor.commands.toggleBulletList,
    "Can toggle ordered list:", !!editor.commands.toggleOrderedList,
    "Can toggle heading:", !!editor.commands.toggleHeading
  );

  const addLink = () => {
    if (linkUrl) {
      // Check if the URL has a protocol, if not add https://
      const url = linkUrl.match(/^https?:\/\//) ? linkUrl : `https://${linkUrl}`;
      
      // Set the link
      editor
        .chain()
        .focus()
        .setLink({ href: url, target: '_blank' })
        .run();
      
      // Reset and close
      setLinkUrl("");
      setLinkOpen(false);
    }
  };

  return (
    <div className="flex flex-wrap items-center gap-1 rounded-md border p-1">
      <TooltipProvider delayDuration={300}>
        {/* Text formatting */}
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Bold');
            editor.chain().focus().toggleBold().run();
            console.log('Bold active:', editor.isActive('bold'));
          }}
          isActive={editor.isActive('bold')}
          title="Bold"
        >
          <Bold className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Italic');
            editor.chain().focus().toggleItalic().run();
            console.log('Italic active:', editor.isActive('italic'));
          }}
          isActive={editor.isActive('italic')}
          title="Italic"
        >
          <Italic className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Underline');
            editor.chain().focus().toggleUnderline().run();
            console.log('Underline active:', editor.isActive('underline'));
          }}
          isActive={editor.isActive('underline')}
          title="Underline"
        >
          <Underline className="h-4 w-4" />
        </ToolbarButton>
        
        <Separator orientation="vertical" className="mx-1 h-6" />
        
        {/* Heading buttons */}
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Heading 1');
            editor.chain().focus().toggleHeading({ level: 1 }).run();
            console.log('H1 active:', editor.isActive('heading', { level: 1 }));
          }}
          isActive={editor.isActive('heading', { level: 1 })}
          title="Heading 1"
        >
          <Heading1 className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Heading 2');
            editor.chain().focus().toggleHeading({ level: 2 }).run();
            console.log('H2 active:', editor.isActive('heading', { level: 2 }));
          }}
          isActive={editor.isActive('heading', { level: 2 })}
          title="Heading 2"
        >
          <Heading2 className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Heading 3');
            editor.chain().focus().toggleHeading({ level: 3 }).run();
            console.log('H3 active:', editor.isActive('heading', { level: 3 }));
          }}
          isActive={editor.isActive('heading', { level: 3 })}
          title="Heading 3"
        >
          <Heading3 className="h-4 w-4" />
        </ToolbarButton>
        
        <Separator orientation="vertical" className="mx-1 h-6" />
        
        {/* Lists */}
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Bullet List');
            editor.chain().focus().toggleBulletList().run();
            console.log('Bullet list active:', editor.isActive('bulletList'));
          }}
          isActive={editor.isActive('bulletList')}
          title="Bullet List"
        >
          <List className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Ordered List');
            editor.chain().focus().toggleOrderedList().run();
            console.log('Ordered list active:', editor.isActive('orderedList'));
          }}
          isActive={editor.isActive('orderedList')}
          title="Numbered List"
        >
          <ListOrdered className="h-4 w-4" />
        </ToolbarButton>
        
        <Separator orientation="vertical" className="mx-1 h-6" />
        
        {/* Text alignment */}
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Align Left');
            editor.chain().focus().setTextAlign('left').run();
            console.log('Align left active:', editor.isActive({ textAlign: 'left' }));
          }}
          isActive={editor.isActive({ textAlign: 'left' })}
          title="Align Left"
        >
          <AlignLeft className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Align Center');
            editor.chain().focus().setTextAlign('center').run();
            console.log('Align center active:', editor.isActive({ textAlign: 'center' }));
          }}
          isActive={editor.isActive({ textAlign: 'center' })}
          title="Align Center"
        >
          <AlignCenter className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Align Right');
            editor.chain().focus().setTextAlign('right').run();
            console.log('Align right active:', editor.isActive({ textAlign: 'right' }));
          }}
          isActive={editor.isActive({ textAlign: 'right' })}
          title="Align Right"
        >
          <AlignRight className="h-4 w-4" />
        </ToolbarButton>
        
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            console.log('Toggling Justify');
            editor.chain().focus().setTextAlign('justify').run();
            console.log('Justify active:', editor.isActive({ textAlign: 'justify' }));
          }}
          isActive={editor.isActive({ textAlign: 'justify' })}
          title="Justify"
        >
          <AlignJustify className="h-4 w-4" />
        </ToolbarButton>
        
        <Separator orientation="vertical" className="mx-1 h-6" />
        
        {/* Insert link */}
        <Popover open={linkOpen} onOpenChange={setLinkOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost" 
              size="sm" 
              className={`h-8 w-8 p-0 ${editor.isActive('link') ? 'bg-muted' : ''}`}
              title="Insert Link"
              aria-label="Insert Link"
            >
              <LinkIcon className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-3">
            <div className="space-y-3">
              <h4 className="font-medium leading-none">Insert Link</h4>
              <div className="space-y-2">
                <Label htmlFor="url">URL</Label>
                <Input 
                  id="url"
                  value={linkUrl} 
                  onChange={(e) => setLinkUrl(e.target.value)}
                  placeholder="https://example.com" 
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setLinkOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={addLink}
                  disabled={!linkUrl}
                >
                  Insert
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Image upload button - functionality to be added */}
        <ToolbarButton
          onClick={(e) => {
            e.preventDefault();
            // Image upload functionality to be implemented
            console.log("Image upload clicked");
          }}
          isActive={false}
          title="Insert Image"
        >
          {/* eslint-disable-next-line jsx-a11y/alt-text */}
          <Image className="h-4 w-4" />
        </ToolbarButton>

        <Separator orientation="vertical" className="mx-1 h-6" />
        
        {/* Table tools */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              title="Table"
              aria-label="Table"
            >
              <Table className="h-4 w-4" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-2">
            <div className="grid gap-2">
              <div className="space-y-1">
                <h4 className="font-medium text-sm">Tables</h4>
                <p className="text-xs text-muted-foreground">Insert and modify tables.</p>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
                >
                  <Table className="h-4 w-4 mr-1" />
                  Insert Table
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().deleteTable().run()}
                  disabled={!editor.isActive('table')}
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete Table
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().addColumnBefore().run()}
                  disabled={!editor.isActive('table')}
                >
                  <ColumnsIcon className="h-4 w-4 mr-1" />
                  Add Column Before
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().addColumnAfter().run()}
                  disabled={!editor.isActive('table')}
                >
                  <ColumnsIcon className="h-4 w-4 mr-1" />
                  Add Column After
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().deleteColumn().run()}
                  disabled={!editor.isActive('table')}
                >
                  <ColumnsIcon className="h-4 w-4 mr-1" />
                  Delete Column
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().addRowBefore().run()}
                  disabled={!editor.isActive('table')}
                >
                  <RowsIcon className="h-4 w-4 mr-1" />
                  Add Row Before
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().addRowAfter().run()}
                  disabled={!editor.isActive('table')}
                >
                  <RowsIcon className="h-4 w-4 mr-1" />
                  Add Row After
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().deleteRow().run()}
                  disabled={!editor.isActive('table')}
                >
                  <RowsIcon className="h-4 w-4 mr-1" />
                  Delete Row
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().toggleHeaderRow().run()}
                  disabled={!editor.isActive('table')}
                >
                  <RowsIcon className="h-4 w-4 mr-1" />
                  Toggle Header Row
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().toggleHeaderColumn().run()}
                  disabled={!editor.isActive('table')}
                >
                  <ColumnsIcon className="h-4 w-4 mr-1" />
                  Toggle Header Column
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().mergeCells().run()}
                  disabled={!editor.isActive('table')}
                >
                  <Table className="h-4 w-4 mr-1" />
                  Merge Cells
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs py-1"
                  onClick={() => editor.chain().focus().splitCell().run()}
                  disabled={!editor.isActive('table')}
                >
                  <Table className="h-4 w-4 mr-1" />
                  Split Cell
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </TooltipProvider>
    </div>
  );
}
