import { Suspense } from 'react';
import { TaskList } from '@/components/tasks/task-list';

export default function TasksPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">My Tasks</h1>
        <p className="text-muted-foreground">View and manage your assigned tasks</p>
      </div>
      
      <Suspense fallback={
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-24 bg-muted/20 rounded-lg animate-pulse" />
          ))}
        </div>
      }>
        <TaskList />
      </Suspense>
    </div>
  );
}
