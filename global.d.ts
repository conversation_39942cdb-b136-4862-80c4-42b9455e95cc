// Jest testing functions
declare function describe(name: string, fn: () => void): void;
declare function beforeAll(fn: () => void | Promise<void>): void;
declare function afterAll(fn: () => void | Promise<void>): void;
declare function beforeEach(fn: () => void | Promise<void>): void;
declare function afterEach(fn: () => void | Promise<void>): void;
declare function test(name: string, fn: () => void | Promise<void>, timeout?: number): void;
declare function it(name: string, fn: () => void | Promise<void>, timeout?: number): void;
declare function expect(value: any): any;

// Extend Crypto interface with the randomUUID method
interface Crypto {
  randomUUID(): `${string}-${string}-${string}-${string}-${string}`;
} 