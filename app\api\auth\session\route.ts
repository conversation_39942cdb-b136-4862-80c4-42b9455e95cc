import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export const dynamic = 'force-dynamic';

  /**
   * GET /api/auth/session
   *
   * Retrieves the current user session, if any.
   *
   * @returns {NextResponse} JSON response containing the user information, or
   *   null if no session is present.
   * @throws {Error} If there's an error while retrieving the session.
   */
export async function GET() {
  try {
    const userCookie = cookies().get('user');
    
    if (!userCookie?.value) {
      return NextResponse.json({ user: null });
    }

    const user = JSON.parse(userCookie.value);
    return NextResponse.json({ user });
  } catch (error) {
    console.error('Session error:', error);
    return NextResponse.json(
      { 
        status: 'error',
        code: 'SESSION_ERROR',
        message: 'Failed to get session' 
      },
      { status: 500 }
    );
  }
}
