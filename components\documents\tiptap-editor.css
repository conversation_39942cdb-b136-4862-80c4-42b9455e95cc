/* TipTap Editor Styles */

/* Ensure headings display correctly */
.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

/* Ensure lists display correctly */
.ProseMirror ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 0.5rem 0;
}

.ProseMirror li {
  margin-bottom: 0.25rem;
}

/* Ensure lists are properly nested */
.ProseMirror ul ul,
.ProseMirror ol ol,
.ProseMirror ul ol,
.ProseMirror ol ul {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Fix focus issues */
.ProseMirror:focus {
  outline: none;
}

/* Add some spacing between paragraphs */
.ProseMirror p {
  margin-bottom: 0.75rem;
}

/* Table Styles */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1rem 0;
  overflow: hidden;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: bold;
}

.dark .ProseMirror th {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.ProseMirror td,
.ProseMirror th {
  border: 2px solid #e5e7eb;
  padding: 0.5rem;
  position: relative;
  vertical-align: top;
  box-sizing: border-box;
  min-width: 3rem;
}

.dark .ProseMirror td,
.dark .ProseMirror th {
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* Selected cells */
.ProseMirror .selectedCell:after {
  background: rgba(200, 200, 255, 0.4);
  content: "";
  inset: 0;
  pointer-events: none;
  position: absolute;
  z-index: 2;
}

/* Table column and row resize handles */
.tableColumnResizer {
  background-color: #adf;
  cursor: col-resize;
  height: 100%;
  position: absolute;
  right: -2px;
  top: 0;
  width: 4px;
  z-index: 10;
}

.tableColumnResizer:hover,
.tableColumnResizer.dragging {
  background-color: #68b;
}

/* Table wrapper for scrolling */
.tableWrapper {
  overflow-x: auto;
  max-width: 100%;
  margin-bottom: 1rem;
}
