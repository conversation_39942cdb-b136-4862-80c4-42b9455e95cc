'use client';

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AlertTriangle, CheckCircle2, Clock } from 'lucide-react';

interface Node {
  id: string;
  name: string;
  status: 'pending' | 'completed' | 'delayed' | 'at_risk';
  progress: number;
  dueDate: string;
}

interface Link {
  source: string;
  target: string;
}

interface DependencyGraphProps {
  nodes: Node[];
  links: Link[];
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return '#22c55e';
    case 'delayed':
      return '#dc2626';
    case 'at_risk':
      return '#eab308';
    default:
      return '#64748b';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle2 className="h-3 w-3 mr-1" />;
    case 'delayed':
    case 'at_risk':
      return <AlertTriangle className="h-3 w-3 mr-1" />;
    default:
      return <Clock className="h-3 w-3 mr-1" />;
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

export function DependencyGraph({ nodes, links }: DependencyGraphProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || !tooltipRef.current || nodes.length === 0) return;

    // Clear previous graph
    d3.select(svgRef.current).selectAll('*').remove();

    const width = 800;
    const height = 600;
    const nodeRadius = 40;

    // Create SVG
    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Create tooltip
    const tooltip = d3.select(tooltipRef.current)
      .style('position', 'absolute')
      .style('visibility', 'hidden')
      .style('background-color', 'white')
      .style('border', '1px solid #ccc')
      .style('border-radius', '4px')
      .style('padding', '8px')
      .style('pointer-events', 'none')
      .style('z-index', '10');

    // Create force simulation
    const simulation = d3.forceSimulation(nodes as d3.SimulationNodeDatum[])
      .force('link', d3.forceLink(links).id((d: any) => d.id).distance(150))
      .force('charge', d3.forceManyBody().strength(-1000))
      .force('x', d3.forceX())
      .force('y', d3.forceY())
      .force('collision', d3.forceCollide().radius(nodeRadius * 1.2));

    // Create arrow marker
    svg.append('defs').append('marker')
      .attr('id', 'arrowhead')
      .attr('viewBox', '-0 -5 10 10')
      .attr('refX', nodeRadius + 10)
      .attr('refY', 0)
      .attr('orient', 'auto')
      .attr('markerWidth', 6)
      .attr('markerHeight', 6)
      .attr('xoverflow', 'visible')
      .append('svg:path')
      .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
      .attr('fill', '#999')
      .style('stroke', 'none');

    // Draw links
    const link = svg.append('g')
      .selectAll('line')
      .data(links)
      .join('line')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', 2)
      .attr('marker-end', 'url(#arrowhead)');

    // Create node groups
    const node = svg.append('g')
      .selectAll('g')
      .data(nodes)
      .join('g')
      .call(d3.drag<any, any>()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended));

    // Add circles to nodes
    node.append('circle')
      .attr('r', nodeRadius)
      .attr('fill', d => getStatusColor(d.status))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2);

    // Add progress arc to nodes
    const arc = d3.arc()
      .innerRadius(nodeRadius - 3)
      .outerRadius(nodeRadius)
      .startAngle(0)
      .endAngle((d: any) => (d.progress / 100) * 2 * Math.PI);

    node.append('path')
      .attr('d', arc as any)
      .attr('fill', '#fff');

    // Add text to nodes
    node.append('text')
      .text(d => d.name)
      .attr('text-anchor', 'middle')
      .attr('dy', '0.3em')
      .attr('fill', '#fff')
      .style('font-size', '12px');

    // Handle node hover
    node.on('mouseover', function(event, d) {
      tooltip
        .style('visibility', 'visible')
        .html(`
          <div class="font-medium">${d.name}</div>
          <div class="text-sm text-gray-500">Due: ${formatDate(d.dueDate)}</div>
          <div class="text-sm">Progress: ${d.progress}%</div>
          <div class="text-sm">Status: ${d.status}</div>
        `);
    })
    .on('mousemove', function(event) {
      tooltip
        .style('top', (event.pageY - 10) + 'px')
        .style('left', (event.pageX + 10) + 'px');
    })
    .on('mouseout', function() {
      tooltip.style('visibility', 'hidden');
    });

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', d => (d as any).source.x)
        .attr('y1', d => (d as any).source.y)
        .attr('x2', d => (d as any).target.x)
        .attr('y2', d => (d as any).target.y);

      node
        .attr('transform', d => `translate(${(d as any).x},${(d as any).y})`);
    });

    // Drag functions
    function dragstarted(event: any) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      event.subject.fx = event.subject.x;
      event.subject.fy = event.subject.y;
    }

    function dragged(event: any) {
      event.subject.fx = event.x;
      event.subject.fy = event.y;
    }

    function dragended(event: any) {
      if (!event.active) simulation.alphaTarget(0);
      event.subject.fx = null;
      event.subject.fy = null;
    }

    return () => {
      simulation.stop();
    };
  }, [nodes, links]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Milestone Dependencies</CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[600px] w-full">
          <div className="relative">
            <svg ref={svgRef} />
            <div ref={tooltipRef} />
          </div>
        </ScrollArea>
        <div className="mt-4 flex items-center gap-4">
          <div className="text-sm font-medium">Legend:</div>
          <Badge variant="default" className="bg-green-500">
            {getStatusIcon('completed')} Completed
          </Badge>
          <Badge variant="destructive">
            {getStatusIcon('delayed')} Delayed
          </Badge>
          <Badge variant="secondary" className="bg-yellow-500">
            {getStatusIcon('at_risk')} At Risk
          </Badge>
          <Badge variant="secondary">
            {getStatusIcon('pending')} Pending
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
