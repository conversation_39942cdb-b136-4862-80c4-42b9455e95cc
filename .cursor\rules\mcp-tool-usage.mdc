---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---

## Sequential Thinking
- Use **Sequential Thinking MCP** for debugging, troubleshooting, complex problem-solving, and detailed project planning.
- Avoid excessive recursive calls; trigger intelligently only when new progress or significant information is possible.

## Browser Tools
- **Browser Tools MCP** requires user confirmation. Always recommend the user explicitly start the server and ensure a Chromium browser is running before using Browser Tools MCP.
- Let the user explicitly instruct <PERSON>urs<PERSON> when Browser Tools should be used.
- Remind user to disable puppeteer before attempting to use

## Information Gathering (Brave Search, Puppeteer, FireCrawl)
- Use **Brave Search, Puppeteer, and FireCrawl MCP servers** when troubleshooting, searching documentation, or exploring similar user issues.
- Combine effectively with **Sequential Thinking MCP** to refine solutions and acquire up-to-date information.
- Prioritize reliable and concise sources.

## GitHub MCP
- Commit and push code changes to GitHub using the **GitHub MCP server** after every successful test.
- Ensure commits are clear, descriptive, and incremental.
- Never overwrite or unintentionally alter files like README.md or other critical documentation without explicit user approval.

## Memeory MCP
1. User Identification:
   - You should assume that you are interacting with default_user
   - If you have not identified default_user, proactively try to do so.

2. Memory Retrieval:
   - Always begin your chat by saying only "Remembering..." and retrieve all relevant information from your knowledge graph
   - Always refer to your knowledge graph as your "memory"

3. Memory
   - While conversing with the user, be attentive to any new information that falls into these categories:
     a) Basic Identity (age, gender, location, job title, education level, etc.)
     b) Behaviors (interests, habits, etc.)
     c) Preferences (communication style, preferred language, etc.)
     d) Goals (goals, targets, aspirations, etc.)
     e) Relationships (personal and professional relationships up to 3 degrees of separation)

4. Memory Update:
   - If any new information was gathered during the interaction, update your memory as follows:
     a) Create entities for recurring organizations, people, and significant events
     b) Connect them to the current entities using relations
     b) Store facts about them as observations
