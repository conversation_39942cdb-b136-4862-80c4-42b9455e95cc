import axios from 'axios';
import { getSAMLArtifact } from '@/lib/samlUtils';
import { TaskListRequest, TaskListResponse, TaskItem } from '@/types/task';

const TASK_LIST_ENDPOINT = '/DatacentrixBidManagement/entities/LifecycleTask/lists/BidDefaultTaskList';

/**
 * Fetches tasks from the Appworks API
 * @param filters - Optional filter parameters
 * @returns Promise with the task list response
 */
export async function fetchTasks(filters?: Partial<TaskListRequest>): Promise<TaskListResponse> {
  try {
    console.log('Fetching tasks through proxy API...');
    
    // Get the SAML artifact for authentication
    const samlArtifact = await getSAMLArtifact();
    
    if (!samlArtifact) {
      console.warn('No SAML artifact found for task fetch');
      // Return empty response structure to match the expected type
      return {
        page: { skip: 0, top: 0, count: 0 },
        _links: { self: { href: '' }, first: { href: '' } },
        _embedded: { BidDefaultTaskList: [] }
      };
    }
    
    // Build request headers with SAML artifact
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Add SAML artifact to headers
    if (samlArtifact.value) {
      headers['SAMLart'] = samlArtifact.value;
    }
    
    // Make the request to our proxy API endpoint
    const response = await fetch('/api/tasks', {
      method: 'POST',
      headers,
      body: filters ? JSON.stringify(filters) : undefined
    });

    if (!response.ok) {
      console.error(`API error: ${response.status} ${response.statusText}`);
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Successfully fetched tasks:', {
      taskCount: data?._embedded?.BidDefaultTaskList?.length || 0,
    });
    
    return data as TaskListResponse;
  } catch (error) {
    console.error('Error fetching tasks:', error);
    // Return empty response on error to prevent UI crashes
    return {
      page: { skip: 0, top: 0, count: 0 },
      _links: { self: { href: '' }, first: { href: '' } },
      _embedded: { BidDefaultTaskList: [] }
    };
  }
}

/**
 * Gets tasks assigned to the current user
 * @returns Promise with an array of tasks assigned to the current user
 */
export async function getMyTasks(): Promise<TaskItem[]> {
  try {
    console.log('Fetching user tasks...');
    
    // The filtering is now handled by the API route
    const response = await fetchTasks();
    
    const tasks = response._embedded?.BidDefaultTaskList || [];
    console.log(`Found ${tasks.length} tasks assigned to the user`);
    
    return tasks;
  } catch (error) {
    console.error('Error fetching user tasks:', error);
    return [];
  }
}

/**
 * Updates a task's status
 * @param taskId - The ID of the task to update
 * @param status - The new status
 * @returns Promise that resolves when the update is complete
 */
export async function updateTaskStatus(taskId: string, status: string): Promise<boolean> {
  try {
    // In a real implementation, you would make an API call to update the task status
    console.log(`Updating task ${taskId} status to ${status}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return true;
  } catch (error) {
    console.error('Error updating task status:', error);
    return false;
  }
}
