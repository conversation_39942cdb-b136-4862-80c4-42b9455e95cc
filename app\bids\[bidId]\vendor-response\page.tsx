'use client';

import { use<PERSON>ara<PERSON> } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileUpload } from '@/components/ui/file-upload';
import { useToast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';

const vendorResponseSchema = z.object({
  companyName: z.string().min(2, 'Company name must be at least 2 characters'),
  contactName: z.string().min(2, 'Contact name must be at least 2 characters'),
  contactEmail: z.string().email('Invalid email address'),
  contactPhone: z.string().min(10, 'Phone number must be at least 10 digits'),
  proposalSummary: z.string().min(100, 'Proposal summary must be at least 100 characters'),
  technicalApproach: z.string().min(200, 'Technical approach must be at least 200 characters'),
  pricingDetails: z.string().min(50, 'Pricing details must be at least 50 characters'),
  implementationTimeline: z.string().min(50, 'Implementation timeline must be at least 50 characters'),
  supportingDocuments: z.array(z.custom<File>()).optional(),
});

type VendorResponseForm = z.infer<typeof vendorResponseSchema>;

export default function VendorResponsePage() {
  const params = useParams();
  const bidId = params.bidId as string;
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formData, setFormData] = useState<VendorResponseForm | null>(null);

  const form = useForm<VendorResponseForm>({
    resolver: zodResolver(vendorResponseSchema),
    defaultValues: {
      companyName: '',
      contactName: '',
      contactEmail: '',
      contactPhone: '',
      proposalSummary: '',
      technicalApproach: '',
      pricingDetails: '',
      implementationTimeline: '',
      supportingDocuments: [],
    },
  });

  const handleFileUpload = async (files: File[]): Promise<void> => {
    try {
      // Validate file types and sizes
      const invalidFiles = files.filter(
        file => !['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
          .includes(file.type)
      );

      if (invalidFiles.length > 0) {
        throw new Error('Invalid file type. Please upload only PDF or Word documents.');
      }

      const oversizedFiles = files.filter(file => file.size > 50 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        throw new Error('One or more files exceed the 50MB size limit.');
      }

      // TODO: Implement file upload to backend storage
      console.log('Uploading files:', files);
      toast({
        title: "Files Uploaded",
        description: `Successfully uploaded ${files.length} file(s)`,
      });
    } catch (error) {
      console.error('Error uploading files:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "There was an error uploading your files. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (data: VendorResponseForm) => {
    setFormData(data);
    setShowConfirmDialog(true);
  };

  const handleConfirmedSubmit = async () => {
    if (!formData) return;
    
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit vendor response
      console.log('Submitting vendor response:', formData);
      
      toast({
        title: "Response Submitted",
        description: "Your vendor response has been submitted successfully.",
      });
      
      // Reset form after successful submission
      form.reset();
    } catch (error) {
      console.error('Error submitting vendor response:', error);
      
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setShowConfirmDialog(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Vendor Response Submission</CardTitle>
          <CardDescription>
            Please provide your detailed response to bid #{bidId}. All fields marked with * are required.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your company name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter contact person name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Email *</FormLabel>
                      <FormControl>
                        <Input type="email" placeholder="Enter contact email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Phone *</FormLabel>
                      <FormControl>
                        <Input type="tel" placeholder="Enter contact phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="proposalSummary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Proposal Summary *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Provide a comprehensive summary of your proposal"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="technicalApproach"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Technical Approach *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your technical approach and methodology"
                        className="min-h-[150px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pricingDetails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pricing Details *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Provide detailed pricing information"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="implementationTimeline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Implementation Timeline *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Outline your proposed implementation timeline"
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <FormLabel>Supporting Documents</FormLabel>
                <FileUpload 
                  onUpload={handleFileUpload}
                  maxFiles={5}
                  maxSize={50 * 1024 * 1024} // 50MB
                  acceptedFileTypes={['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip']}
                />
              </div>

              <Button 
                type="submit" 
                className="w-full"
                disabled={isSubmitting}
                aria-label="Submit vendor response"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Response'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Submission</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to submit this vendor response? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmedSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Confirm'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
